#[test_only]
module freelancer_escrow::escrow_tests {
    use std::signer;
    use std::string;
    use std::vector;
    use aptos_framework::coin;
    use aptos_framework::aptos_coin::{Self, AptosCoin};
    use aptos_framework::timestamp;
    use aptos_framework::account;
    use freelancer_escrow::escrow;
    use freelancer_escrow::security;

    // Test accounts
    const ADMIN: address = @0x100;
    const SECURITY_ADMIN: address = @0x101;
    const CLIENT: address = @0x200;
    const FREELANCER: address = @0x300;
    const REGISTRY: address = @0x400;

    // Test constants
    const ESCROW_AMOUNT: u64 = **********; // 10 APT
    const DEADLINE_HOURS: u64 = 168; // 7 days

    #[test(aptos_framework = @0x1, admin = @0x100, security_admin = @0x101, registry = @0x400)]
    public fun test_initialize_system(
        aptos_framework: &signer,
        admin: &signer,
        security_admin: &signer,
        registry: &signer,
    ) {
        // Initialize timestamp and coin
        timestamp::set_time_has_started_for_testing(aptos_framework);
        let (burn_cap, mint_cap) = aptos_coin::initialize_for_test(aptos_framework);
        
        // Initialize security system
        security::initialize_security(security_admin);
        
        // Initialize escrow system
        escrow::initialize(registry, signer::address_of(security_admin));
        
        // Verify initialization
        let all_escrows = escrow::get_all_escrows(signer::address_of(registry));
        assert!(vector::length(&all_escrows) == 0, 1);
        
        coin::destroy_burn_cap(burn_cap);
        coin::destroy_mint_cap(mint_cap);
    }

    #[test(aptos_framework = @0x1, admin = @0x100, security_admin = @0x101, registry = @0x400, client = @0x200, freelancer = @0x300)]
    public fun test_create_escrow_success(
        aptos_framework: &signer,
        admin: &signer,
        security_admin: &signer,
        registry: &signer,
        client: &signer,
        freelancer: &signer,
    ) {
        // Setup
        timestamp::set_time_has_started_for_testing(aptos_framework);
        let (burn_cap, mint_cap) = aptos_coin::initialize_for_test(aptos_framework);
        
        // Create accounts
        account::create_account_for_test(signer::address_of(client));
        account::create_account_for_test(signer::address_of(freelancer));
        account::create_account_for_test(signer::address_of(registry));
        account::create_account_for_test(signer::address_of(security_admin));
        
        // Initialize systems
        security::initialize_security(security_admin);
        escrow::initialize(registry, signer::address_of(security_admin));
        
        // Mint coins for client
        let coins = coin::mint<AptosCoin>(ESCROW_AMOUNT * 2, &mint_cap);
        coin::deposit(signer::address_of(client), coins);
        
        // Create escrow
        escrow::create_escrow(
            client,
            signer::address_of(freelancer),
            ESCROW_AMOUNT,
            string::utf8(b"Website Development"),
            string::utf8(b"Build a responsive website"),
            DEADLINE_HOURS,
            signer::address_of(registry),
        );
        
        // Verify escrow creation
        let (client_addr, freelancer_addr, amount, title, description, status, deadline, work_submission, created_at, funds_value) = 
            escrow::get_escrow_details(signer::address_of(client), 1);
        
        assert!(client_addr == signer::address_of(client), 2);
        assert!(freelancer_addr == signer::address_of(freelancer), 3);
        assert!(amount == ESCROW_AMOUNT, 4);
        assert!(status == 0, 5); // STATUS_CREATED
        assert!(funds_value == 0, 6); // Not funded yet
        
        coin::destroy_burn_cap(burn_cap);
        coin::destroy_mint_cap(mint_cap);
    }

    #[test(aptos_framework = @0x1, admin = @0x100, security_admin = @0x101, registry = @0x400, client = @0x200, freelancer = @0x300)]
    public fun test_full_escrow_workflow(
        aptos_framework: &signer,
        admin: &signer,
        security_admin: &signer,
        registry: &signer,
        client: &signer,
        freelancer: &signer,
    ) {
        // Setup
        timestamp::set_time_has_started_for_testing(aptos_framework);
        let (burn_cap, mint_cap) = aptos_coin::initialize_for_test(aptos_framework);
        
        // Create accounts
        account::create_account_for_test(signer::address_of(client));
        account::create_account_for_test(signer::address_of(freelancer));
        account::create_account_for_test(signer::address_of(registry));
        account::create_account_for_test(signer::address_of(security_admin));
        
        // Initialize systems
        security::initialize_security(security_admin);
        escrow::initialize(registry, signer::address_of(security_admin));
        
        // Mint coins for client
        let coins = coin::mint<AptosCoin>(ESCROW_AMOUNT * 2, &mint_cap);
        coin::deposit(signer::address_of(client), coins);
        
        // Step 1: Create escrow
        escrow::create_escrow(
            client,
            signer::address_of(freelancer),
            ESCROW_AMOUNT,
            string::utf8(b"Website Development"),
            string::utf8(b"Build a responsive website"),
            DEADLINE_HOURS,
            signer::address_of(registry),
        );
        
        // Verify status is CREATED
        assert!(escrow::get_escrow_status(signer::address_of(client), 1) == 0, 7);
        
        // Step 2: Fund escrow
        escrow::fund_escrow(
            client,
            1,
            signer::address_of(registry),
        );
        
        // Verify status is FUNDED
        assert!(escrow::get_escrow_status(signer::address_of(client), 1) == 1, 8);
        
        // Step 3: Submit work
        escrow::submit_work(
            freelancer,
            signer::address_of(client),
            1,
            string::utf8(b"Website completed: https://example.com"),
            signer::address_of(registry),
        );
        
        // Verify status is WORK_SUBMITTED
        assert!(escrow::get_escrow_status(signer::address_of(client), 1) == 2, 9);
        
        // Step 4: Approve and release payment
        let freelancer_balance_before = coin::balance<AptosCoin>(signer::address_of(freelancer));
        
        escrow::approve_and_release(
            client,
            1,
            signer::address_of(registry),
        );
        
        // Verify status is COMPLETED
        assert!(escrow::get_escrow_status(signer::address_of(client), 1) == 3, 10);
        
        // Verify freelancer received payment
        let freelancer_balance_after = coin::balance<AptosCoin>(signer::address_of(freelancer));
        assert!(freelancer_balance_after == freelancer_balance_before + ESCROW_AMOUNT, 11);
        
        coin::destroy_burn_cap(burn_cap);
        coin::destroy_mint_cap(mint_cap);
    }

    #[test(aptos_framework = @0x1, admin = @0x100, security_admin = @0x101, registry = @0x400, client = @0x200, freelancer = @0x300)]
    public fun test_dispute_workflow(
        aptos_framework: &signer,
        admin: &signer,
        security_admin: &signer,
        registry: &signer,
        client: &signer,
        freelancer: &signer,
    ) {
        // Setup (similar to full workflow test)
        timestamp::set_time_has_started_for_testing(aptos_framework);
        let (burn_cap, mint_cap) = aptos_coin::initialize_for_test(aptos_framework);
        
        // Create accounts
        account::create_account_for_test(signer::address_of(client));
        account::create_account_for_test(signer::address_of(freelancer));
        account::create_account_for_test(signer::address_of(registry));
        account::create_account_for_test(signer::address_of(security_admin));
        
        // Initialize systems
        security::initialize_security(security_admin);
        escrow::initialize(registry, signer::address_of(security_admin));
        
        // Mint coins for client
        let coins = coin::mint<AptosCoin>(ESCROW_AMOUNT * 2, &mint_cap);
        coin::deposit(signer::address_of(client), coins);
        
        // Create and fund escrow
        escrow::create_escrow(
            client,
            signer::address_of(freelancer),
            ESCROW_AMOUNT,
            string::utf8(b"Website Development"),
            string::utf8(b"Build a responsive website"),
            DEADLINE_HOURS,
            signer::address_of(registry),
        );
        
        escrow::fund_escrow(client, 1, signer::address_of(registry));
        
        // Submit work
        escrow::submit_work(
            freelancer,
            signer::address_of(client),
            1,
            string::utf8(b"Website completed: https://example.com"),
            signer::address_of(registry),
        );
        
        // Client disputes the work
        escrow::dispute_escrow(
            client,
            signer::address_of(client),
            1,
            signer::address_of(registry),
        );
        
        // Verify status is DISPUTED
        assert!(escrow::get_escrow_status(signer::address_of(client), 1) == 4, 12);
        
        // Admin resolves dispute in favor of freelancer
        let freelancer_balance_before = coin::balance<AptosCoin>(signer::address_of(freelancer));
        
        escrow::resolve_dispute(
            registry, // registry is admin in this test
            signer::address_of(client),
            1,
            true, // pay freelancer
            signer::address_of(registry),
        );
        
        // Verify status is COMPLETED
        assert!(escrow::get_escrow_status(signer::address_of(client), 1) == 3, 13);
        
        // Verify freelancer received payment
        let freelancer_balance_after = coin::balance<AptosCoin>(signer::address_of(freelancer));
        assert!(freelancer_balance_after == freelancer_balance_before + ESCROW_AMOUNT, 14);
        
        coin::destroy_burn_cap(burn_cap);
        coin::destroy_mint_cap(mint_cap);
    }
}
