{"ast": null, "code": "import _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nvar FORMAT_KEYS = ['YYYY', 'MM', 'DD', 'HH', 'mm', 'ss', 'SSS'];\n// Use Chinese character to avoid conflict with the mask format\nvar REPLACE_KEY = '顧';\nvar MaskFormat = /*#__PURE__*/function () {\n  function MaskFormat(format) {\n    _classCallCheck(this, MaskFormat);\n    _defineProperty(this, \"format\", void 0);\n    _defineProperty(this, \"maskFormat\", void 0);\n    _defineProperty(this, \"cells\", void 0);\n    _defineProperty(this, \"maskCells\", void 0);\n    this.format = format;\n\n    // Generate mask format\n    var replaceKeys = FORMAT_KEYS.map(function (key) {\n      return \"(\".concat(key, \")\");\n    }).join('|');\n    var replaceReg = new RegExp(replaceKeys, 'g');\n    this.maskFormat = format.replace(replaceReg,\n    // Use Chinese character to avoid user use it in format\n    function (key) {\n      return REPLACE_KEY.repeat(key.length);\n    });\n\n    // Generate cells\n    var cellReg = new RegExp(\"(\".concat(FORMAT_KEYS.join('|'), \")\"));\n    var strCells = (format.split(cellReg) || []).filter(function (str) {\n      return str;\n    });\n    var offset = 0;\n    this.cells = strCells.map(function (text) {\n      var mask = FORMAT_KEYS.includes(text);\n      var start = offset;\n      var end = offset + text.length;\n      offset = end;\n      return {\n        text: text,\n        mask: mask,\n        start: start,\n        end: end\n      };\n    });\n\n    // Mask cells\n    this.maskCells = this.cells.filter(function (cell) {\n      return cell.mask;\n    });\n  }\n  _createClass(MaskFormat, [{\n    key: \"getSelection\",\n    value: function getSelection(maskCellIndex) {\n      var _ref = this.maskCells[maskCellIndex] || {},\n        start = _ref.start,\n        end = _ref.end;\n      return [start || 0, end || 0];\n    }\n\n    /** Check given text match format */\n  }, {\n    key: \"match\",\n    value: function match(text) {\n      for (var i = 0; i < this.maskFormat.length; i += 1) {\n        var maskChar = this.maskFormat[i];\n        var textChar = text[i];\n        if (!textChar || maskChar !== REPLACE_KEY && maskChar !== textChar) {\n          return false;\n        }\n      }\n      return true;\n    }\n\n    /** Get mask cell count */\n  }, {\n    key: \"size\",\n    value: function size() {\n      return this.maskCells.length;\n    }\n  }, {\n    key: \"getMaskCellIndex\",\n    value: function getMaskCellIndex(anchorIndex) {\n      var closetDist = Number.MAX_SAFE_INTEGER;\n      var closetIndex = 0;\n      for (var i = 0; i < this.maskCells.length; i += 1) {\n        var _this$maskCells$i = this.maskCells[i],\n          start = _this$maskCells$i.start,\n          end = _this$maskCells$i.end;\n        if (anchorIndex >= start && anchorIndex <= end) {\n          return i;\n        }\n        var dist = Math.min(Math.abs(anchorIndex - start), Math.abs(anchorIndex - end));\n        if (dist < closetDist) {\n          closetDist = dist;\n          closetIndex = i;\n        }\n      }\n      return closetIndex;\n    }\n  }]);\n  return MaskFormat;\n}();\nexport { MaskFormat as default };", "map": {"version": 3, "names": ["_classCallCheck", "_createClass", "_defineProperty", "FORMAT_KEYS", "REPLACE_KEY", "MaskFormat", "format", "<PERSON><PERSON><PERSON><PERSON>", "map", "key", "concat", "join", "replaceReg", "RegExp", "maskFormat", "replace", "repeat", "length", "cellReg", "str<PERSON><PERSON><PERSON>", "split", "filter", "str", "offset", "cells", "text", "mask", "includes", "start", "end", "<PERSON><PERSON><PERSON><PERSON>", "cell", "value", "getSelection", "maskCellIndex", "_ref", "match", "i", "maskChar", "textChar", "size", "getMaskCellIndex", "anchorIndex", "closetDist", "Number", "MAX_SAFE_INTEGER", "closetIndex", "_this$maskCells$i", "dist", "Math", "min", "abs", "default"], "sources": ["C:/Users/<USER>/Desktop/Aptos/freelancer-escrow-frontend/node_modules/rc-picker/es/PickerInput/Selector/MaskFormat.js"], "sourcesContent": ["import _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nvar FORMAT_KEYS = ['YYYY', 'MM', 'DD', 'HH', 'mm', 'ss', 'SSS'];\n// Use Chinese character to avoid conflict with the mask format\nvar REPLACE_KEY = '顧';\nvar MaskFormat = /*#__PURE__*/function () {\n  function MaskFormat(format) {\n    _classCallCheck(this, MaskFormat);\n    _defineProperty(this, \"format\", void 0);\n    _defineProperty(this, \"maskFormat\", void 0);\n    _defineProperty(this, \"cells\", void 0);\n    _defineProperty(this, \"maskCells\", void 0);\n    this.format = format;\n\n    // Generate mask format\n    var replaceKeys = FORMAT_KEYS.map(function (key) {\n      return \"(\".concat(key, \")\");\n    }).join('|');\n    var replaceReg = new RegExp(replaceKeys, 'g');\n    this.maskFormat = format.replace(replaceReg,\n    // Use Chinese character to avoid user use it in format\n    function (key) {\n      return REPLACE_KEY.repeat(key.length);\n    });\n\n    // Generate cells\n    var cellReg = new RegExp(\"(\".concat(FORMAT_KEYS.join('|'), \")\"));\n    var strCells = (format.split(cellReg) || []).filter(function (str) {\n      return str;\n    });\n    var offset = 0;\n    this.cells = strCells.map(function (text) {\n      var mask = FORMAT_KEYS.includes(text);\n      var start = offset;\n      var end = offset + text.length;\n      offset = end;\n      return {\n        text: text,\n        mask: mask,\n        start: start,\n        end: end\n      };\n    });\n\n    // Mask cells\n    this.maskCells = this.cells.filter(function (cell) {\n      return cell.mask;\n    });\n  }\n  _createClass(MaskFormat, [{\n    key: \"getSelection\",\n    value: function getSelection(maskCellIndex) {\n      var _ref = this.maskCells[maskCellIndex] || {},\n        start = _ref.start,\n        end = _ref.end;\n      return [start || 0, end || 0];\n    }\n\n    /** Check given text match format */\n  }, {\n    key: \"match\",\n    value: function match(text) {\n      for (var i = 0; i < this.maskFormat.length; i += 1) {\n        var maskChar = this.maskFormat[i];\n        var textChar = text[i];\n        if (!textChar || maskChar !== REPLACE_KEY && maskChar !== textChar) {\n          return false;\n        }\n      }\n      return true;\n    }\n\n    /** Get mask cell count */\n  }, {\n    key: \"size\",\n    value: function size() {\n      return this.maskCells.length;\n    }\n  }, {\n    key: \"getMaskCellIndex\",\n    value: function getMaskCellIndex(anchorIndex) {\n      var closetDist = Number.MAX_SAFE_INTEGER;\n      var closetIndex = 0;\n      for (var i = 0; i < this.maskCells.length; i += 1) {\n        var _this$maskCells$i = this.maskCells[i],\n          start = _this$maskCells$i.start,\n          end = _this$maskCells$i.end;\n        if (anchorIndex >= start && anchorIndex <= end) {\n          return i;\n        }\n        var dist = Math.min(Math.abs(anchorIndex - start), Math.abs(anchorIndex - end));\n        if (dist < closetDist) {\n          closetDist = dist;\n          closetIndex = i;\n        }\n      }\n      return closetIndex;\n    }\n  }]);\n  return MaskFormat;\n}();\nexport { MaskFormat as default };"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,eAAe,MAAM,2CAA2C;AACvE,IAAIC,WAAW,GAAG,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC;AAC/D;AACA,IAAIC,WAAW,GAAG,GAAG;AACrB,IAAIC,UAAU,GAAG,aAAa,YAAY;EACxC,SAASA,UAAUA,CAACC,MAAM,EAAE;IAC1BN,eAAe,CAAC,IAAI,EAAEK,UAAU,CAAC;IACjCH,eAAe,CAAC,IAAI,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;IACvCA,eAAe,CAAC,IAAI,EAAE,YAAY,EAAE,KAAK,CAAC,CAAC;IAC3CA,eAAe,CAAC,IAAI,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;IACtCA,eAAe,CAAC,IAAI,EAAE,WAAW,EAAE,KAAK,CAAC,CAAC;IAC1C,IAAI,CAACI,MAAM,GAAGA,MAAM;;IAEpB;IACA,IAAIC,WAAW,GAAGJ,WAAW,CAACK,GAAG,CAAC,UAAUC,GAAG,EAAE;MAC/C,OAAO,GAAG,CAACC,MAAM,CAACD,GAAG,EAAE,GAAG,CAAC;IAC7B,CAAC,CAAC,CAACE,IAAI,CAAC,GAAG,CAAC;IACZ,IAAIC,UAAU,GAAG,IAAIC,MAAM,CAACN,WAAW,EAAE,GAAG,CAAC;IAC7C,IAAI,CAACO,UAAU,GAAGR,MAAM,CAACS,OAAO,CAACH,UAAU;IAC3C;IACA,UAAUH,GAAG,EAAE;MACb,OAAOL,WAAW,CAACY,MAAM,CAACP,GAAG,CAACQ,MAAM,CAAC;IACvC,CAAC,CAAC;;IAEF;IACA,IAAIC,OAAO,GAAG,IAAIL,MAAM,CAAC,GAAG,CAACH,MAAM,CAACP,WAAW,CAACQ,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC;IAChE,IAAIQ,QAAQ,GAAG,CAACb,MAAM,CAACc,KAAK,CAACF,OAAO,CAAC,IAAI,EAAE,EAAEG,MAAM,CAAC,UAAUC,GAAG,EAAE;MACjE,OAAOA,GAAG;IACZ,CAAC,CAAC;IACF,IAAIC,MAAM,GAAG,CAAC;IACd,IAAI,CAACC,KAAK,GAAGL,QAAQ,CAACX,GAAG,CAAC,UAAUiB,IAAI,EAAE;MACxC,IAAIC,IAAI,GAAGvB,WAAW,CAACwB,QAAQ,CAACF,IAAI,CAAC;MACrC,IAAIG,KAAK,GAAGL,MAAM;MAClB,IAAIM,GAAG,GAAGN,MAAM,GAAGE,IAAI,CAACR,MAAM;MAC9BM,MAAM,GAAGM,GAAG;MACZ,OAAO;QACLJ,IAAI,EAAEA,IAAI;QACVC,IAAI,EAAEA,IAAI;QACVE,KAAK,EAAEA,KAAK;QACZC,GAAG,EAAEA;MACP,CAAC;IACH,CAAC,CAAC;;IAEF;IACA,IAAI,CAACC,SAAS,GAAG,IAAI,CAACN,KAAK,CAACH,MAAM,CAAC,UAAUU,IAAI,EAAE;MACjD,OAAOA,IAAI,CAACL,IAAI;IAClB,CAAC,CAAC;EACJ;EACAzB,YAAY,CAACI,UAAU,EAAE,CAAC;IACxBI,GAAG,EAAE,cAAc;IACnBuB,KAAK,EAAE,SAASC,YAAYA,CAACC,aAAa,EAAE;MAC1C,IAAIC,IAAI,GAAG,IAAI,CAACL,SAAS,CAACI,aAAa,CAAC,IAAI,CAAC,CAAC;QAC5CN,KAAK,GAAGO,IAAI,CAACP,KAAK;QAClBC,GAAG,GAAGM,IAAI,CAACN,GAAG;MAChB,OAAO,CAACD,KAAK,IAAI,CAAC,EAAEC,GAAG,IAAI,CAAC,CAAC;IAC/B;;IAEA;EACF,CAAC,EAAE;IACDpB,GAAG,EAAE,OAAO;IACZuB,KAAK,EAAE,SAASI,KAAKA,CAACX,IAAI,EAAE;MAC1B,KAAK,IAAIY,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACvB,UAAU,CAACG,MAAM,EAAEoB,CAAC,IAAI,CAAC,EAAE;QAClD,IAAIC,QAAQ,GAAG,IAAI,CAACxB,UAAU,CAACuB,CAAC,CAAC;QACjC,IAAIE,QAAQ,GAAGd,IAAI,CAACY,CAAC,CAAC;QACtB,IAAI,CAACE,QAAQ,IAAID,QAAQ,KAAKlC,WAAW,IAAIkC,QAAQ,KAAKC,QAAQ,EAAE;UAClE,OAAO,KAAK;QACd;MACF;MACA,OAAO,IAAI;IACb;;IAEA;EACF,CAAC,EAAE;IACD9B,GAAG,EAAE,MAAM;IACXuB,KAAK,EAAE,SAASQ,IAAIA,CAAA,EAAG;MACrB,OAAO,IAAI,CAACV,SAAS,CAACb,MAAM;IAC9B;EACF,CAAC,EAAE;IACDR,GAAG,EAAE,kBAAkB;IACvBuB,KAAK,EAAE,SAASS,gBAAgBA,CAACC,WAAW,EAAE;MAC5C,IAAIC,UAAU,GAAGC,MAAM,CAACC,gBAAgB;MACxC,IAAIC,WAAW,GAAG,CAAC;MACnB,KAAK,IAAIT,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACP,SAAS,CAACb,MAAM,EAAEoB,CAAC,IAAI,CAAC,EAAE;QACjD,IAAIU,iBAAiB,GAAG,IAAI,CAACjB,SAAS,CAACO,CAAC,CAAC;UACvCT,KAAK,GAAGmB,iBAAiB,CAACnB,KAAK;UAC/BC,GAAG,GAAGkB,iBAAiB,CAAClB,GAAG;QAC7B,IAAIa,WAAW,IAAId,KAAK,IAAIc,WAAW,IAAIb,GAAG,EAAE;UAC9C,OAAOQ,CAAC;QACV;QACA,IAAIW,IAAI,GAAGC,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAACT,WAAW,GAAGd,KAAK,CAAC,EAAEqB,IAAI,CAACE,GAAG,CAACT,WAAW,GAAGb,GAAG,CAAC,CAAC;QAC/E,IAAImB,IAAI,GAAGL,UAAU,EAAE;UACrBA,UAAU,GAAGK,IAAI;UACjBF,WAAW,GAAGT,CAAC;QACjB;MACF;MACA,OAAOS,WAAW;IACpB;EACF,CAAC,CAAC,CAAC;EACH,OAAOzC,UAAU;AACnB,CAAC,CAAC,CAAC;AACH,SAASA,UAAU,IAAI+C,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}