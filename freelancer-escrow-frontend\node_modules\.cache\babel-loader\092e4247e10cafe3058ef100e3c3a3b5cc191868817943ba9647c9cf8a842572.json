{"ast": null, "code": "var __classPrivateFieldGet = this && this.__classPrivateFieldGet || function (receiver, state, kind, f) {\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n  return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n};\nvar __classPrivateFieldSet = this && this.__classPrivateFieldSet || function (receiver, state, value, kind, f) {\n  if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n  return kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value), value;\n};\nvar _RegisterWalletEvent_detail;\n/**\n * Register a {@link \"@wallet-standard/base\".Wallet} as a Standard Wallet with the app.\n *\n * This dispatches a {@link \"@wallet-standard/base\".WindowRegisterWalletEvent} to notify the app that the Wallet is\n * ready to be registered.\n *\n * This also adds a listener for {@link \"@wallet-standard/base\".WindowAppReadyEvent} to listen for a notification from\n * the app that the app is ready to register the Wallet.\n *\n * This combination of event dispatch and listener guarantees that the Wallet will be registered synchronously as soon\n * as the app is ready whether the Wallet loads before or after the app.\n *\n * @param wallet Wallet to register.\n *\n * @group Wallet\n */\nexport function registerWallet(wallet) {\n  const callback = ({\n    register\n  }) => register(wallet);\n  try {\n    window.dispatchEvent(new RegisterWalletEvent(callback));\n  } catch (error) {\n    console.error('wallet-standard:register-wallet event could not be dispatched\\n', error);\n  }\n  try {\n    window.addEventListener('wallet-standard:app-ready', ({\n      detail: api\n    }) => callback(api));\n  } catch (error) {\n    console.error('wallet-standard:app-ready event listener could not be added\\n', error);\n  }\n}\nclass RegisterWalletEvent extends Event {\n  get detail() {\n    return __classPrivateFieldGet(this, _RegisterWalletEvent_detail, \"f\");\n  }\n  get type() {\n    return 'wallet-standard:register-wallet';\n  }\n  constructor(callback) {\n    super('wallet-standard:register-wallet', {\n      bubbles: false,\n      cancelable: false,\n      composed: false\n    });\n    _RegisterWalletEvent_detail.set(this, void 0);\n    __classPrivateFieldSet(this, _RegisterWalletEvent_detail, callback, \"f\");\n  }\n  /** @deprecated */\n  preventDefault() {\n    throw new Error('preventDefault cannot be called');\n  }\n  /** @deprecated */\n  stopImmediatePropagation() {\n    throw new Error('stopImmediatePropagation cannot be called');\n  }\n  /** @deprecated */\n  stopPropagation() {\n    throw new Error('stopPropagation cannot be called');\n  }\n}\n_RegisterWalletEvent_detail = new WeakMap();\n/**\n * @deprecated Use {@link registerWallet} instead.\n *\n * @group Deprecated\n */\nexport function DEPRECATED_registerWallet(wallet) {\n  var _a;\n  registerWallet(wallet);\n  try {\n    ((_a = window.navigator).wallets || (_a.wallets = [])).push(({\n      register\n    }) => register(wallet));\n  } catch (error) {\n    console.error('window.navigator.wallets could not be pushed\\n', error);\n  }\n}", "map": {"version": 3, "names": ["registerWallet", "wallet", "callback", "register", "window", "dispatchEvent", "RegisterWalletEvent", "error", "console", "addEventListener", "detail", "api", "Event", "__classPrivateFieldGet", "_RegisterWalletEvent_detail", "type", "constructor", "bubbles", "cancelable", "composed", "set", "__classPrivateFieldSet", "preventDefault", "Error", "stopImmediatePropagation", "stopPropagation", "DEPRECATED_registerWallet", "_a", "navigator", "wallets", "push"], "sources": ["C:\\Users\\<USER>\\Desktop\\Aptos\\freelancer-escrow-frontend\\node_modules\\@wallet-standard\\wallet\\src\\register.ts"], "sourcesContent": ["import type {\n    DEPRECATED_WalletsWindow,\n    Wallet,\n    WalletEventsWindow,\n    WindowRegisterWalletEvent,\n    WindowRegisterWalletEventCallback,\n} from '@wallet-standard/base';\n\n/**\n * Register a {@link \"@wallet-standard/base\".Wallet} as a Standard Wallet with the app.\n *\n * This dispatches a {@link \"@wallet-standard/base\".WindowRegisterWalletEvent} to notify the app that the Wallet is\n * ready to be registered.\n *\n * This also adds a listener for {@link \"@wallet-standard/base\".WindowAppReadyEvent} to listen for a notification from\n * the app that the app is ready to register the Wallet.\n *\n * This combination of event dispatch and listener guarantees that the Wallet will be registered synchronously as soon\n * as the app is ready whether the Wallet loads before or after the app.\n *\n * @param wallet Wallet to register.\n *\n * @group Wallet\n */\nexport function registerWallet(wallet: Wallet): void {\n    const callback: WindowRegisterWalletEventCallback = ({ register }) => register(wallet);\n    try {\n        (window as WalletEventsWindow).dispatchEvent(new RegisterWalletEvent(callback));\n    } catch (error) {\n        console.error('wallet-standard:register-wallet event could not be dispatched\\n', error);\n    }\n    try {\n        (window as WalletEventsWindow).addEventListener('wallet-standard:app-ready', ({ detail: api }) =>\n            callback(api)\n        );\n    } catch (error) {\n        console.error('wallet-standard:app-ready event listener could not be added\\n', error);\n    }\n}\n\nclass RegisterWalletEvent extends Event implements WindowRegisterWalletEvent {\n    readonly #detail: WindowRegisterWalletEventCallback;\n\n    get detail() {\n        return this.#detail;\n    }\n\n    get type() {\n        return 'wallet-standard:register-wallet' as const;\n    }\n\n    constructor(callback: WindowRegisterWalletEventCallback) {\n        super('wallet-standard:register-wallet', {\n            bubbles: false,\n            cancelable: false,\n            composed: false,\n        });\n        this.#detail = callback;\n    }\n\n    /** @deprecated */\n    preventDefault(): never {\n        throw new Error('preventDefault cannot be called');\n    }\n\n    /** @deprecated */\n    stopImmediatePropagation(): never {\n        throw new Error('stopImmediatePropagation cannot be called');\n    }\n\n    /** @deprecated */\n    stopPropagation(): never {\n        throw new Error('stopPropagation cannot be called');\n    }\n}\n\n/**\n * @deprecated Use {@link registerWallet} instead.\n *\n * @group Deprecated\n */\nexport function DEPRECATED_registerWallet(wallet: Wallet): void {\n    registerWallet(wallet);\n    try {\n        ((window as DEPRECATED_WalletsWindow).navigator.wallets ||= []).push(({ register }) => register(wallet));\n    } catch (error) {\n        console.error('window.navigator.wallets could not be pushed\\n', error);\n    }\n}\n"], "mappings": ";;;;;;;;;;;;AAQA;;;;;;;;;;;;;;;;AAgBA,OAAM,SAAUA,cAAcA,CAACC,MAAc;EACzC,MAAMC,QAAQ,GAAsCA,CAAC;IAAEC;EAAQ,CAAE,KAAKA,QAAQ,CAACF,MAAM,CAAC;EACtF,IAAI;IACCG,MAA6B,CAACC,aAAa,CAAC,IAAIC,mBAAmB,CAACJ,QAAQ,CAAC,CAAC;EACnF,CAAC,CAAC,OAAOK,KAAK,EAAE;IACZC,OAAO,CAACD,KAAK,CAAC,iEAAiE,EAAEA,KAAK,CAAC;EAC3F;EACA,IAAI;IACCH,MAA6B,CAACK,gBAAgB,CAAC,2BAA2B,EAAE,CAAC;MAAEC,MAAM,EAAEC;IAAG,CAAE,KACzFT,QAAQ,CAACS,GAAG,CAAC,CAChB;EACL,CAAC,CAAC,OAAOJ,KAAK,EAAE;IACZC,OAAO,CAACD,KAAK,CAAC,+DAA+D,EAAEA,KAAK,CAAC;EACzF;AACJ;AAEA,MAAMD,mBAAoB,SAAQM,KAAK;EAGnC,IAAIF,MAAMA,CAAA;IACN,OAAOG,sBAAA,KAAI,EAAAC,2BAAA,MAAQ;EACvB;EAEA,IAAIC,IAAIA,CAAA;IACJ,OAAO,iCAA0C;EACrD;EAEAC,YAAYd,QAA2C;IACnD,KAAK,CAAC,iCAAiC,EAAE;MACrCe,OAAO,EAAE,KAAK;MACdC,UAAU,EAAE,KAAK;MACjBC,QAAQ,EAAE;KACb,CAAC;IAfGL,2BAAA,CAAAM,GAAA;IAgBLC,sBAAA,KAAI,EAAAP,2BAAA,EAAWZ,QAAQ;EAC3B;EAEA;EACAoB,cAAcA,CAAA;IACV,MAAM,IAAIC,KAAK,CAAC,iCAAiC,CAAC;EACtD;EAEA;EACAC,wBAAwBA,CAAA;IACpB,MAAM,IAAID,KAAK,CAAC,2CAA2C,CAAC;EAChE;EAEA;EACAE,eAAeA,CAAA;IACX,MAAM,IAAIF,KAAK,CAAC,kCAAkC,CAAC;EACvD;;;AAGJ;;;;;AAKA,OAAM,SAAUG,yBAAyBA,CAACzB,MAAc;;EACpDD,cAAc,CAACC,MAAM,CAAC;EACtB,IAAI;IACA,EAAA0B,EAAA,GAAEvB,MAAmC,CAACwB,SAAS,EAACC,OAAO,KAAAF,EAAA,CAAPE,OAAO,GAAK,EAAE,GAAEC,IAAI,CAAC,CAAC;MAAE3B;IAAQ,CAAE,KAAKA,QAAQ,CAACF,MAAM,CAAC,CAAC;EAC5G,CAAC,CAAC,OAAOM,KAAK,EAAE;IACZC,OAAO,CAACD,KAAK,CAAC,gDAAgD,EAAEA,KAAK,CAAC;EAC1E;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}