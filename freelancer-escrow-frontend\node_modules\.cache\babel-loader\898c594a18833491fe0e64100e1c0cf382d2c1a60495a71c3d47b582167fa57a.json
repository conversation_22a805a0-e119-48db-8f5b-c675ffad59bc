{"ast": null, "code": "import rules from \"../rule\";\nimport { isEmptyValue } from \"../util\";\nvar type = function type(rule, value, callback, source, options) {\n  var ruleType = rule.type;\n  var errors = [];\n  var validate = rule.required || !rule.required && source.hasOwnProperty(rule.field);\n  if (validate) {\n    if (isEmptyValue(value, ruleType) && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options, ruleType);\n    if (!isEmptyValue(value, ruleType)) {\n      rules.type(rule, value, source, errors, options);\n    }\n  }\n  callback(errors);\n};\nexport default type;", "map": {"version": 3, "names": ["rules", "isEmptyValue", "type", "rule", "value", "callback", "source", "options", "ruleType", "errors", "validate", "required", "hasOwnProperty", "field"], "sources": ["C:/Users/<USER>/Desktop/Aptos/freelancer-escrow-frontend/node_modules/@rc-component/async-validator/es/validator/type.js"], "sourcesContent": ["import rules from \"../rule\";\nimport { isEmptyValue } from \"../util\";\nvar type = function type(rule, value, callback, source, options) {\n  var ruleType = rule.type;\n  var errors = [];\n  var validate = rule.required || !rule.required && source.hasOwnProperty(rule.field);\n  if (validate) {\n    if (isEmptyValue(value, ruleType) && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options, ruleType);\n    if (!isEmptyValue(value, ruleType)) {\n      rules.type(rule, value, source, errors, options);\n    }\n  }\n  callback(errors);\n};\nexport default type;"], "mappings": "AAAA,OAAOA,KAAK,MAAM,SAAS;AAC3B,SAASC,YAAY,QAAQ,SAAS;AACtC,IAAIC,IAAI,GAAG,SAASA,IAAIA,CAACC,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,OAAO,EAAE;EAC/D,IAAIC,QAAQ,GAAGL,IAAI,CAACD,IAAI;EACxB,IAAIO,MAAM,GAAG,EAAE;EACf,IAAIC,QAAQ,GAAGP,IAAI,CAACQ,QAAQ,IAAI,CAACR,IAAI,CAACQ,QAAQ,IAAIL,MAAM,CAACM,cAAc,CAACT,IAAI,CAACU,KAAK,CAAC;EACnF,IAAIH,QAAQ,EAAE;IACZ,IAAIT,YAAY,CAACG,KAAK,EAAEI,QAAQ,CAAC,IAAI,CAACL,IAAI,CAACQ,QAAQ,EAAE;MACnD,OAAON,QAAQ,CAAC,CAAC;IACnB;IACAL,KAAK,CAACW,QAAQ,CAACR,IAAI,EAAEC,KAAK,EAAEE,MAAM,EAAEG,MAAM,EAAEF,OAAO,EAAEC,QAAQ,CAAC;IAC9D,IAAI,CAACP,YAAY,CAACG,KAAK,EAAEI,QAAQ,CAAC,EAAE;MAClCR,KAAK,CAACE,IAAI,CAACC,IAAI,EAAEC,KAAK,EAAEE,MAAM,EAAEG,MAAM,EAAEF,OAAO,CAAC;IAClD;EACF;EACAF,QAAQ,CAACI,MAAM,CAAC;AAClB,CAAC;AACD,eAAeP,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}