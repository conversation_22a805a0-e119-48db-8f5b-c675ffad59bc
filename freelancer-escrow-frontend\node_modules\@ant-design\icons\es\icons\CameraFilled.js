import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import CameraFilledSvg from "@ant-design/icons-svg/es/asn/CameraFilled";
import AntdIcon from "../components/AntdIcon";
var CameraFilled = function CameraFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: CameraFilledSvg
  }));
};

/**![camera](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg2NCAyNjBINzI4bC0zMi40LTkwLjhhMzIuMDcgMzIuMDcgMCAwMC0zMC4yLTIxLjJIMzU4LjZjLTEzLjUgMC0yNS42IDguNS0zMC4xIDIxLjJMMjk2IDI2MEgxNjBjLTQ0LjIgMC04MCAzNS44LTgwIDgwdjQ1NmMwIDQ0LjIgMzUuOCA4MCA4MCA4MGg3MDRjNDQuMiAwIDgwLTM1LjggODAtODBWMzQwYzAtNDQuMi0zNS44LTgwLTgwLTgwek01MTIgNzE2Yy04OC40IDAtMTYwLTcxLjYtMTYwLTE2MHM3MS42LTE2MCAxNjAtMTYwIDE2MCA3MS42IDE2MCAxNjAtNzEuNiAxNjAtMTYwIDE2MHptLTk2LTE2MGE5NiA5NiAwIDEwMTkyIDAgOTYgOTYgMCAxMC0xOTIgMHoiIC8+PC9zdmc+) */
var RefIcon = /*#__PURE__*/React.forwardRef(CameraFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'CameraFilled';
}
export default RefIcon;