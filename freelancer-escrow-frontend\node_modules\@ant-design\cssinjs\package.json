{"name": "@ant-design/cssinjs", "version": "1.24.0", "description": "Component level cssinjs resolution for antd", "keywords": ["react", "c<PERSON><PERSON>s", "antd", "ant-design"], "main": "./lib/index", "module": "./es/index", "files": ["es", "lib", "dist"], "homepage": "https://github.com/ant-design/cssinjs", "repository": {"type": "git", "url": "**************:ant-design/cssinjs.git"}, "bugs": {"url": "http://github.com/ant-design/cssinjs/issues"}, "license": "MIT", "scripts": {"start": "dumi dev", "dev": "father dev", "docs:build": "dumi build", "docs:deploy": "gh-pages -d .doc", "compile": "father build", "gh-pages": "npm run docs:build && npm run docs:deploy", "prepublishOnly": "npm run compile && np --yolo --no-publish --any-branch", "postpublish": "npm run gh-pages", "lint": "eslint src/ --ext .ts,.tsx,.jsx,.js,.md", "prettier": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "pretty-quick": "pretty-quick", "test": "vitest --watch=false", "test:watch": "vitest", "coverage": "vitest run --coverage"}, "dependencies": {"@babel/runtime": "^7.11.1", "@emotion/hash": "^0.8.0", "@emotion/unitless": "^0.7.5", "classnames": "^2.3.1", "csstype": "^3.1.3", "rc-util": "^5.35.0", "stylis": "^4.3.4"}, "devDependencies": {"@ctrl/tinycolor": "^3.4.0", "@rc-component/father-plugin": "^1.0.1", "@testing-library/jest-dom": "^5.16.3", "@testing-library/react": "^13.0.0", "@types/classnames": "^2.2.9", "@types/enzyme": "^3.10.11", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@types/responselike": "^1.0.0", "@types/stylis": "^4.0.2", "@types/testing-library__jest-dom": "^5.14.5", "@umijs/fabric": "^2.0.8", "@vitest/coverage-c8": "^0.31.0", "coveralls": "^3.0.6", "cross-env": "^7.0.2", "dumi": "^2.1.13", "eslint": "^7.0.0", "father": "^4.0.0", "gh-pages": "^3.1.0", "husky": "^4.2.5", "jsdom": "^22.0.0", "np": "^7.0.0", "prettier": "^2.0.5", "pretty-quick": "^3.0.0", "react": "^18.0.0", "react-dom": "^18.0.0", "typescript": "^4.0.5", "vitest": "^0.31.0"}, "peerDependencies": {"react": ">=16.0.0", "react-dom": ">=16.0.0"}, "husky": {"hooks": {"pre-commit": "pretty-quick --staged"}}}