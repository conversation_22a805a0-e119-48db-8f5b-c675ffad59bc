{"name": "sockjs", "description": "SockJS-node is a server counterpart of SockJS-client a JavaScript library that provides a WebSocket-like object in the browser. SockJS gives you a coherent, cross-browser, Javascript API which creates a low latency, full duplex, cross-domain communication channel between the browser and the web server.", "version": "0.3.24", "author": "<PERSON><PERSON>", "bugs": {"url": "https://github.com/sockjs/sockjs-node/issues"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "dependencies": {"faye-websocket": "^0.11.3", "uuid": "^8.3.2", "websocket-driver": "^0.7.4"}, "devDependencies": {"coffeescript": "^1.12.7"}, "homepage": "https://github.com/sockjs/sockjs-node", "keywords": ["websockets", "websocket"], "license": "MIT", "main": "index", "repository": {"type": "git", "url": "https://github.com/sockjs/sockjs-node.git"}, "scripts": {"version": "make build && git add Changelog", "postversion": "npm publish", "postpublish": "git push origin --all && git push origin --tags"}}