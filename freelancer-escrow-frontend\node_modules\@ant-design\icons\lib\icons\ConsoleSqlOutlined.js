"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _ConsoleSqlOutlined = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/ConsoleSqlOutlined"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var ConsoleSqlOutlined = function ConsoleSqlOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _ConsoleSqlOutlined.default
  }));
};

/**![console-sql](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PHN0eWxlIC8+PC9kZWZzPjxwYXRoIGQ9Ik0zMDEuMyA0OTYuN2MtMjMuOCAwLTQwLjItMTAuNS00MS42LTI2LjlIMjA1Yy45IDQzLjQgMzYuOSA3MC4zIDkzLjkgNzAuMyA1OS4xIDAgOTUtMjguNCA5NS03NS41IDAtMzUuOC0yMC01NS45LTY0LjUtNjQuNWwtMjkuMS01LjZjLTIzLjgtNC43LTMzLjgtMTEuOS0zMy44LTI0LjIgMC0xNSAxMy4zLTI0LjUgMzMuNC0yNC41IDIwLjEgMCAzNS4zIDExLjEgMzYuNiAyN2g1M2MtLjktNDEuNy0zNy41LTcwLjMtOTAuMy03MC4zLTU0LjQgMC04OS43IDI4LjktODkuNyA3MyAwIDM1LjUgMjEuMiA1OCA2Mi41IDY1LjhsMjkuNyA1LjljMjUuOCA1LjIgMzUuNiAxMS45IDM1LjYgMjQuNC4xIDE0LjctMTQuNSAyNS4xLTM2IDI1LjF6IiAvPjxwYXRoIGQ9Ik05MjggMTQwSDk2Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnY0OTZjMCAxNy43IDE0LjMgMzIgMzIgMzJoMzgwdjExMkgzMDRjLTguOCAwLTE2IDcuMi0xNiAxNnY0OGMwIDQuNCAzLjYgOCA4IDhoNDMyYzQuNCAwIDgtMy42IDgtOHYtNDhjMC04LjgtNy4yLTE2LTE2LTE2SDU0OFY3MDBoMzgwYzE3LjcgMCAzMi0xNC4zIDMyLTMyVjE3MmMwLTE3LjctMTQuMy0zMi0zMi0zMnptLTQwIDQ4OEgxMzZWMjEyaDc1MnY0MTZ6IiAvPjxwYXRoIGQ9Ik04MjguNSA0ODYuN2gtOTUuOFYzMDguNWgtNTcuNFY1MzRoMTUzLjJ6bS0yOTguNiA1My40YzE0LjEgMCAyNy4yLTIgMzkuMS01LjhsMTMuMyAyMC4zaDUzLjNMNjA3LjkgNTExYzIxLjEtMjAgMzMtNTEuMSAzMy04OS44IDAtNzMuMy00My4zLTExOC44LTExMC45LTExOC44cy0xMTEuMiA0NS4zLTExMS4yIDExOC44Yy0uMSA3My43IDQzIDExOC45IDExMS4xIDExOC45em0wLTE5MGMzMS42IDAgNTIuNyAyNy43IDUyLjcgNzEuMSAwIDE2LjctMy42IDMwLjYtMTAgNDAuNWwtNS4yLTYuOWgtNDguOEw1NDIgNDkxYy0zLjkuOS04IDEuNC0xMi4yIDEuNC0zMS43IDAtNTIuOC0yNy41LTUyLjgtNzEuMi4xLTQzLjYgMjEuMi03MS4xIDUyLjktNzEuMXoiIC8+PC9zdmc+) */
var RefIcon = /*#__PURE__*/React.forwardRef(ConsoleSqlOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'ConsoleSqlOutlined';
}
var _default = exports.default = RefIcon;