"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _CopyrightCircleTwoTone = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/CopyrightCircleTwoTone"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var CopyrightCircleTwoTone = function CopyrightCircleTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _CopyrightCircleTwoTone.default
  }));
};

/**![copyright-circle](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMiA2NEMyNjQuNiA2NCA2NCAyNjQuNiA2NCA1MTJzMjAwLjYgNDQ4IDQ0OCA0NDggNDQ4LTIwMC42IDQ0OC00NDhTNzU5LjQgNjQgNTEyIDY0em0wIDgyMGMtMjA1LjQgMC0zNzItMTY2LjYtMzcyLTM3MnMxNjYuNi0zNzIgMzcyLTM3MiAzNzIgMTY2LjYgMzcyIDM3Mi0xNjYuNiAzNzItMzcyIDM3MnoiIGZpbGw9IiMxNjc3ZmYiIC8+PHBhdGggZD0iTTUxMiAxNDBjLTIwNS40IDAtMzcyIDE2Ni42LTM3MiAzNzJzMTY2LjYgMzcyIDM3MiAzNzIgMzcyLTE2Ni42IDM3Mi0zNzItMTY2LjYtMzcyLTM3Mi0zNzJ6bTUuNSA1MzNjNTIuOSAwIDg4LjgtMzEuNyA5My03Ny44LjQtNC4xIDMuOC03LjMgOC03LjNoNTYuOGMyLjYgMCA0LjcgMi4xIDQuNyA0LjcgMCA4Mi42LTY4LjcgMTQxLjQtMTYyLjcgMTQxLjRDNDA3LjQgNzM0IDM0NCA2NjAuOCAzNDQgNTM5LjF2LTUyLjNDMzQ0IDM2NC4yIDQwNy40IDI5MCA1MTcuMyAyOTBjOTQuMyAwIDE2Mi43IDYwLjcgMTYyLjcgMTQ3LjQgMCAyLjYtMi4xIDQuNy00LjcgNC43aC01Ni43Yy00LjIgMC03LjctMy4yLTgtNy40LTQtNDkuNi00MC04My40LTkzLTgzLjQtNjUuMiAwLTEwMi4xIDQ4LjUtMTAyLjIgMTM1LjV2NTIuNmMwIDg1LjcgMzYuOCAxMzMuNiAxMDIuMSAxMzMuNnoiIGZpbGw9IiNlNmY0ZmYiIC8+PHBhdGggZD0iTTUxNy42IDM1MS4zYzUzIDAgODkgMzMuOCA5MyA4My40LjMgNC4yIDMuOCA3LjQgOCA3LjRoNTYuN2MyLjYgMCA0LjctMi4xIDQuNy00LjcgMC04Ni43LTY4LjQtMTQ3LjQtMTYyLjctMTQ3LjRDNDA3LjQgMjkwIDM0NCAzNjQuMiAzNDQgNDg2Ljh2NTIuM0MzNDQgNjYwLjggNDA3LjQgNzM0IDUxNy4zIDczNGM5NCAwIDE2Mi43LTU4LjggMTYyLjctMTQxLjQgMC0yLjYtMi4xLTQuNy00LjctNC43aC01Ni44Yy00LjIgMC03LjYgMy4yLTggNy4zLTQuMiA0Ni4xLTQwLjEgNzcuOC05MyA3Ny44LTY1LjMgMC0xMDIuMS00Ny45LTEwMi4xLTEzMy42di01Mi42Yy4xLTg3IDM3LTEzNS41IDEwMi4yLTEzNS41eiIgZmlsbD0iIzE2NzdmZiIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/React.forwardRef(CopyrightCircleTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'CopyrightCircleTwoTone';
}
var _default = exports.default = RefIcon;