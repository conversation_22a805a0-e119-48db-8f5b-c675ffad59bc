import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import AreaChartOutlinedSvg from "@ant-design/icons-svg/es/asn/AreaChartOutlined";
import AntdIcon from "../components/AntdIcon";
var AreaChartOutlined = function AreaChartOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: AreaChartOutlinedSvg
  }));
};

/**![area-chart](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg4OCA3OTJIMjAwVjE2OGMwLTQuNC0zLjYtOC04LThoLTU2Yy00LjQgMC04IDMuNi04IDh2Njg4YzAgNC40IDMuNiA4IDggOGg3NTJjNC40IDAgOC0zLjYgOC04di01NmMwLTQuNC0zLjYtOC04LTh6bS02MTYtNjRoNTM2YzQuNCAwIDgtMy42IDgtOFYyODRjMC03LjItOC43LTEwLjctMTMuNy01LjdMNTkyIDQ4OC42bC0xMjUuNC0xMjRhOC4wMyA4LjAzIDAgMDAtMTEuMyAwbC0xODkgMTg5LjZhNy44NyA3Ljg3IDAgMDAtMi4zIDUuNlY3MjBjMCA0LjQgMy42IDggOCA4eiIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/React.forwardRef(AreaChartOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'AreaChartOutlined';
}
export default RefIcon;