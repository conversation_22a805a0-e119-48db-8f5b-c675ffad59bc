{"name": "source-map", "description": "Generates and consumes source maps", "version": "0.7.6", "homepage": "https://github.com/mozilla/source-map", "author": "<PERSON> <<EMAIL>>", "contributors": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "usrbincc <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> Smith <<EMAIL>>", "<PERSON> <<EMAIL>>", "azu <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <j<PERSON><PERSON>@walmartlabs.com>", "<PERSON> <jeff<PERSON><PERSON><PERSON>@gmail.com>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "djchie <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"], "repository": {"type": "git", "url": "http://github.com/mozilla/source-map.git"}, "main": "./source-map.js", "types": "./source-map.d.ts", "browser": {"./lib/read-wasm.js": "./lib/read-wasm-browser.js"}, "files": ["source-map.js", "source-map.d.ts", "lib/"], "engines": {"node": ">= 12"}, "license": "BSD-3-<PERSON><PERSON>", "scripts": {"lint": "eslint --fix *.js lib/ test/ --ignore-pattern 'test/source-map-tests/**'", "test": "git submodule update --init --recursive; node test/run-tests.js", "coverage": "c8 --reporter=text --reporter=html npm test", "prettier": "prettier --write .", "clean": "rm -rf coverage", "toc": "doctoc --github --notitle README.md CONTRIBUTING.md"}, "devDependencies": {"c8": "^7.12.0", "doctoc": "^2.2.1", "eslint": "^8.24.0", "eslint-config-prettier": "^8.5.0", "prettier": "^2.7.1"}, "dependencies": {}}