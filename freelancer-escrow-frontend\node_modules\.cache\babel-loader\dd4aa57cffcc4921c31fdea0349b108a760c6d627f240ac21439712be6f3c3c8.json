{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Aptos\\\\freelancer-escrow-frontend\\\\src\\\\App.tsx\";\nimport { Button, Card, ConfigProvider, Space, theme, Typography } from 'antd';\nimport React from 'react';\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Paragraph\n} = Typography;\nconst App = () => {\n  return /*#__PURE__*/_jsxDEV(ConfigProvider, {\n    theme: {\n      algorithm: theme.defaultAlgorithm,\n      token: {\n        colorPrimary: '#1890ff',\n        borderRadius: 8\n      }\n    },\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        padding: '50px',\n        textAlign: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        style: {\n          maxWidth: '800px',\n          margin: '0 auto'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Title, {\n          level: 1,\n          style: {\n            color: '#1890ff'\n          },\n          children: \"FreelanceEscrow\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n          style: {\n            fontSize: '18px',\n            marginBottom: '30px'\n          },\n          children: \"Secure freelance payments on Aptos blockchain\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Space, {\n          direction: \"vertical\",\n          size: \"large\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(Title, {\n              level: 3,\n              children: \"\\uD83C\\uDF89 Application Status\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 29,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n              children: [\"\\u2705 Backend API: Running on http://localhost:5000\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 31,\n                columnNumber: 64\n              }, this), \"\\u2705 Frontend: Successfully compiled\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 32,\n                columnNumber: 50\n              }, this), \"\\u2705 MongoDB: Connected and ready\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 33,\n                columnNumber: 47\n              }, this), \"\\u2705 Smart Contracts: Deployed and tested\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 30,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 28,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(Title, {\n              level: 3,\n              children: \"\\uD83D\\uDE80 Features\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 39,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n              children: [\"\\u2022 Secure escrow smart contracts on Aptos\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 41,\n                columnNumber: 57\n              }, this), \"\\u2022 User authentication and profiles\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 42,\n                columnNumber: 51\n              }, this), \"\\u2022 Project management dashboard\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 43,\n                columnNumber: 47\n              }, this), \"\\u2022 Real-time communication\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 44,\n                columnNumber: 42\n              }, this), \"\\u2022 Dispute resolution system\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 40,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 38,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              size: \"large\",\n              children: \"Get Started\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 50,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              size: \"large\",\n              children: \"Learn More\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 53,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 9,\n    columnNumber: 5\n  }, this);\n};\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["<PERSON><PERSON>", "Card", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Space", "theme", "Typography", "React", "jsxDEV", "_jsxDEV", "Title", "Paragraph", "App", "algorithm", "defaultAlgorithm", "token", "colorPrimary", "borderRadius", "children", "style", "padding", "textAlign", "max<PERSON><PERSON><PERSON>", "margin", "level", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fontSize", "marginBottom", "direction", "size", "type", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Aptos/freelancer-escrow-frontend/src/App.tsx"], "sourcesContent": ["import { Button, Card, ConfigProvider, Space, theme, Typography } from 'antd';\nimport React from 'react';\nimport './App.css';\n\nconst { Title, Paragraph } = Typography;\n\nconst App: React.FC = () => {\n  return (\n    <ConfigProvider\n      theme={{\n        algorithm: theme.defaultAlgorithm,\n        token: {\n          colorPrimary: '#1890ff',\n          borderRadius: 8,\n        },\n      }}\n    >\n      <div style={{ padding: '50px', textAlign: 'center' }}>\n        <Card style={{ maxWidth: '800px', margin: '0 auto' }}>\n          <Title level={1} style={{ color: '#1890ff' }}>\n            FreelanceEscrow\n          </Title>\n          <Paragraph style={{ fontSize: '18px', marginBottom: '30px' }}>\n            Secure freelance payments on Aptos blockchain\n          </Paragraph>\n\n          <Space direction=\"vertical\" size=\"large\">\n            <div>\n              <Title level={3}>🎉 Application Status</Title>\n              <Paragraph>\n                ✅ Backend API: Running on http://localhost:5000<br/>\n                ✅ Frontend: Successfully compiled<br/>\n                ✅ MongoDB: Connected and ready<br/>\n                ✅ Smart Contracts: Deployed and tested\n              </Paragraph>\n            </div>\n\n            <div>\n              <Title level={3}>🚀 Features</Title>\n              <Paragraph>\n                • Secure escrow smart contracts on Aptos<br/>\n                • User authentication and profiles<br/>\n                • Project management dashboard<br/>\n                • Real-time communication<br/>\n                • Dispute resolution system\n              </Paragraph>\n            </div>\n\n            <Space>\n              <Button type=\"primary\" size=\"large\">\n                Get Started\n              </Button>\n              <Button size=\"large\">\n                Learn More\n              </Button>\n            </Space>\n          </Space>\n        </Card>\n      </div>\n    </ConfigProvider>\n  );\n};\n\nexport default App;\n"], "mappings": ";AAAA,SAASA,MAAM,EAAEC,IAAI,EAAEC,cAAc,EAAEC,KAAK,EAAEC,KAAK,EAAEC,UAAU,QAAQ,MAAM;AAC7E,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,MAAM;EAAEC,KAAK;EAAEC;AAAU,CAAC,GAAGL,UAAU;AAEvC,MAAMM,GAAa,GAAGA,CAAA,KAAM;EAC1B,oBACEH,OAAA,CAACN,cAAc;IACbE,KAAK,EAAE;MACLQ,SAAS,EAAER,KAAK,CAACS,gBAAgB;MACjCC,KAAK,EAAE;QACLC,YAAY,EAAE,SAAS;QACvBC,YAAY,EAAE;MAChB;IACF,CAAE;IAAAC,QAAA,eAEFT,OAAA;MAAKU,KAAK,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,SAAS,EAAE;MAAS,CAAE;MAAAH,QAAA,eACnDT,OAAA,CAACP,IAAI;QAACiB,KAAK,EAAE;UAAEG,QAAQ,EAAE,OAAO;UAAEC,MAAM,EAAE;QAAS,CAAE;QAAAL,QAAA,gBACnDT,OAAA,CAACC,KAAK;UAACc,KAAK,EAAE,CAAE;UAACL,KAAK,EAAE;YAAEM,KAAK,EAAE;UAAU,CAAE;UAAAP,QAAA,EAAC;QAE9C;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRpB,OAAA,CAACE,SAAS;UAACQ,KAAK,EAAE;YAAEW,QAAQ,EAAE,MAAM;YAAEC,YAAY,EAAE;UAAO,CAAE;UAAAb,QAAA,EAAC;QAE9D;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAW,CAAC,eAEZpB,OAAA,CAACL,KAAK;UAAC4B,SAAS,EAAC,UAAU;UAACC,IAAI,EAAC,OAAO;UAAAf,QAAA,gBACtCT,OAAA;YAAAS,QAAA,gBACET,OAAA,CAACC,KAAK;cAACc,KAAK,EAAE,CAAE;cAAAN,QAAA,EAAC;YAAqB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC9CpB,OAAA,CAACE,SAAS;cAAAO,QAAA,GAAC,sDACsC,eAAAT,OAAA;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,0CACnB,eAAApB,OAAA;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,uCACR,eAAApB,OAAA;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,+CAErC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eAENpB,OAAA;YAAAS,QAAA,gBACET,OAAA,CAACC,KAAK;cAACc,KAAK,EAAE,CAAE;cAAAN,QAAA,EAAC;YAAW;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACpCpB,OAAA,CAACE,SAAS;cAAAO,QAAA,GAAC,+CAC+B,eAAAT,OAAA;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,2CACX,eAAApB,OAAA;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,uCACT,eAAApB,OAAA;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,kCACV,eAAApB,OAAA;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,oCAEhC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eAENpB,OAAA,CAACL,KAAK;YAAAc,QAAA,gBACJT,OAAA,CAACR,MAAM;cAACiC,IAAI,EAAC,SAAS;cAACD,IAAI,EAAC,OAAO;cAAAf,QAAA,EAAC;YAEpC;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTpB,OAAA,CAACR,MAAM;cAACgC,IAAI,EAAC,OAAO;cAAAf,QAAA,EAAC;YAErB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACQ,CAAC;AAErB,CAAC;AAACM,EAAA,GAvDIvB,GAAa;AAyDnB,eAAeA,GAAG;AAAC,IAAAuB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}