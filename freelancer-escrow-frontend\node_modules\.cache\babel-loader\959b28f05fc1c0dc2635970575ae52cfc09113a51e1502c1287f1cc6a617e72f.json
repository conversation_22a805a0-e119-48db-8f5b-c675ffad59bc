{"ast": null, "code": "/*! scure-base - MIT License (c) 2022 <PERSON> (paulmillr.com) */\nfunction isBytes(a) {\n  return a instanceof Uint8Array || ArrayBuffer.isView(a) && a.constructor.name === 'Uint8Array';\n}\n/** Asserts something is Uint8Array. */\nfunction abytes(b, ...lengths) {\n  if (!isBytes(b)) throw new Error('Uint8Array expected');\n  if (lengths.length > 0 && !lengths.includes(b.length)) throw new Error('Uint8Array expected of length ' + lengths + ', got length=' + b.length);\n}\nfunction isArrayOf(isString, arr) {\n  if (!Array.isArray(arr)) return false;\n  if (arr.length === 0) return true;\n  if (isString) {\n    return arr.every(item => typeof item === 'string');\n  } else {\n    return arr.every(item => Number.isSafeInteger(item));\n  }\n}\n// no abytes: seems to have 10% slowdown. Why?!\nfunction afn(input) {\n  if (typeof input !== 'function') throw new Error('function expected');\n  return true;\n}\nfunction astr(label, input) {\n  if (typeof input !== 'string') throw new Error(`${label}: string expected`);\n  return true;\n}\nfunction anumber(n) {\n  if (!Number.isSafeInteger(n)) throw new Error(`invalid integer: ${n}`);\n}\nfunction aArr(input) {\n  if (!Array.isArray(input)) throw new Error('array expected');\n}\nfunction astrArr(label, input) {\n  if (!isArrayOf(true, input)) throw new Error(`${label}: array of strings expected`);\n}\nfunction anumArr(label, input) {\n  if (!isArrayOf(false, input)) throw new Error(`${label}: array of numbers expected`);\n}\n/**\n * @__NO_SIDE_EFFECTS__\n */\nfunction chain(...args) {\n  const id = a => a;\n  // Wrap call in closure so JIT can inline calls\n  const wrap = (a, b) => c => a(b(c));\n  // Construct chain of args[-1].encode(args[-2].encode([...]))\n  const encode = args.map(x => x.encode).reduceRight(wrap, id);\n  // Construct chain of args[0].decode(args[1].decode(...))\n  const decode = args.map(x => x.decode).reduce(wrap, id);\n  return {\n    encode,\n    decode\n  };\n}\n/**\n * Encodes integer radix representation to array of strings using alphabet and back.\n * Could also be array of strings.\n * @__NO_SIDE_EFFECTS__\n */\nfunction alphabet(letters) {\n  // mapping 1 to \"b\"\n  const lettersA = typeof letters === 'string' ? letters.split('') : letters;\n  const len = lettersA.length;\n  astrArr('alphabet', lettersA);\n  // mapping \"b\" to 1\n  const indexes = new Map(lettersA.map((l, i) => [l, i]));\n  return {\n    encode: digits => {\n      aArr(digits);\n      return digits.map(i => {\n        if (!Number.isSafeInteger(i) || i < 0 || i >= len) throw new Error(`alphabet.encode: digit index outside alphabet \"${i}\". Allowed: ${letters}`);\n        return lettersA[i];\n      });\n    },\n    decode: input => {\n      aArr(input);\n      return input.map(letter => {\n        astr('alphabet.decode', letter);\n        const i = indexes.get(letter);\n        if (i === undefined) throw new Error(`Unknown letter: \"${letter}\". Allowed: ${letters}`);\n        return i;\n      });\n    }\n  };\n}\n/**\n * @__NO_SIDE_EFFECTS__\n */\nfunction join(separator = '') {\n  astr('join', separator);\n  return {\n    encode: from => {\n      astrArr('join.decode', from);\n      return from.join(separator);\n    },\n    decode: to => {\n      astr('join.decode', to);\n      return to.split(separator);\n    }\n  };\n}\n/**\n * Pad strings array so it has integer number of bits\n * @__NO_SIDE_EFFECTS__\n */\nfunction padding(bits, chr = '=') {\n  anumber(bits);\n  astr('padding', chr);\n  return {\n    encode(data) {\n      astrArr('padding.encode', data);\n      while (data.length * bits % 8) data.push(chr);\n      return data;\n    },\n    decode(input) {\n      astrArr('padding.decode', input);\n      let end = input.length;\n      if (end * bits % 8) throw new Error('padding: invalid, string should have whole number of bytes');\n      for (; end > 0 && input[end - 1] === chr; end--) {\n        const last = end - 1;\n        const byte = last * bits;\n        if (byte % 8 === 0) throw new Error('padding: invalid, string has too much padding');\n      }\n      return input.slice(0, end);\n    }\n  };\n}\n/**\n * @__NO_SIDE_EFFECTS__\n */\nfunction normalize(fn) {\n  afn(fn);\n  return {\n    encode: from => from,\n    decode: to => fn(to)\n  };\n}\n/**\n * Slow: O(n^2) time complexity\n */\nfunction convertRadix(data, from, to) {\n  // base 1 is impossible\n  if (from < 2) throw new Error(`convertRadix: invalid from=${from}, base cannot be less than 2`);\n  if (to < 2) throw new Error(`convertRadix: invalid to=${to}, base cannot be less than 2`);\n  aArr(data);\n  if (!data.length) return [];\n  let pos = 0;\n  const res = [];\n  const digits = Array.from(data, d => {\n    anumber(d);\n    if (d < 0 || d >= from) throw new Error(`invalid integer: ${d}`);\n    return d;\n  });\n  const dlen = digits.length;\n  while (true) {\n    let carry = 0;\n    let done = true;\n    for (let i = pos; i < dlen; i++) {\n      const digit = digits[i];\n      const fromCarry = from * carry;\n      const digitBase = fromCarry + digit;\n      if (!Number.isSafeInteger(digitBase) || fromCarry / from !== carry || digitBase - digit !== fromCarry) {\n        throw new Error('convertRadix: carry overflow');\n      }\n      const div = digitBase / to;\n      carry = digitBase % to;\n      const rounded = Math.floor(div);\n      digits[i] = rounded;\n      if (!Number.isSafeInteger(rounded) || rounded * to + carry !== digitBase) throw new Error('convertRadix: carry overflow');\n      if (!done) continue;else if (!rounded) pos = i;else done = false;\n    }\n    res.push(carry);\n    if (done) break;\n  }\n  for (let i = 0; i < data.length - 1 && data[i] === 0; i++) res.push(0);\n  return res.reverse();\n}\nconst gcd = (a, b) => b === 0 ? a : gcd(b, a % b);\nconst radix2carry = /* @__NO_SIDE_EFFECTS__ */(from, to) => from + (to - gcd(from, to));\nconst powers = /* @__PURE__ */(() => {\n  let res = [];\n  for (let i = 0; i < 40; i++) res.push(2 ** i);\n  return res;\n})();\n/**\n * Implemented with numbers, because BigInt is 5x slower\n */\nfunction convertRadix2(data, from, to, padding) {\n  aArr(data);\n  if (from <= 0 || from > 32) throw new Error(`convertRadix2: wrong from=${from}`);\n  if (to <= 0 || to > 32) throw new Error(`convertRadix2: wrong to=${to}`);\n  if (radix2carry(from, to) > 32) {\n    throw new Error(`convertRadix2: carry overflow from=${from} to=${to} carryBits=${radix2carry(from, to)}`);\n  }\n  let carry = 0;\n  let pos = 0; // bitwise position in current element\n  const max = powers[from];\n  const mask = powers[to] - 1;\n  const res = [];\n  for (const n of data) {\n    anumber(n);\n    if (n >= max) throw new Error(`convertRadix2: invalid data word=${n} from=${from}`);\n    carry = carry << from | n;\n    if (pos + from > 32) throw new Error(`convertRadix2: carry overflow pos=${pos} from=${from}`);\n    pos += from;\n    for (; pos >= to; pos -= to) res.push((carry >> pos - to & mask) >>> 0);\n    const pow = powers[pos];\n    if (pow === undefined) throw new Error('invalid carry');\n    carry &= pow - 1; // clean carry, otherwise it will cause overflow\n  }\n  carry = carry << to - pos & mask;\n  if (!padding && pos >= from) throw new Error('Excess padding');\n  if (!padding && carry > 0) throw new Error(`Non-zero padding: ${carry}`);\n  if (padding && pos > 0) res.push(carry >>> 0);\n  return res;\n}\n/**\n * @__NO_SIDE_EFFECTS__\n */\nfunction radix(num) {\n  anumber(num);\n  const _256 = 2 ** 8;\n  return {\n    encode: bytes => {\n      if (!isBytes(bytes)) throw new Error('radix.encode input should be Uint8Array');\n      return convertRadix(Array.from(bytes), _256, num);\n    },\n    decode: digits => {\n      anumArr('radix.decode', digits);\n      return Uint8Array.from(convertRadix(digits, num, _256));\n    }\n  };\n}\n/**\n * If both bases are power of same number (like `2**8 <-> 2**64`),\n * there is a linear algorithm. For now we have implementation for power-of-two bases only.\n * @__NO_SIDE_EFFECTS__\n */\nfunction radix2(bits, revPadding = false) {\n  anumber(bits);\n  if (bits <= 0 || bits > 32) throw new Error('radix2: bits should be in (0..32]');\n  if (radix2carry(8, bits) > 32 || radix2carry(bits, 8) > 32) throw new Error('radix2: carry overflow');\n  return {\n    encode: bytes => {\n      if (!isBytes(bytes)) throw new Error('radix2.encode input should be Uint8Array');\n      return convertRadix2(Array.from(bytes), 8, bits, !revPadding);\n    },\n    decode: digits => {\n      anumArr('radix2.decode', digits);\n      return Uint8Array.from(convertRadix2(digits, bits, 8, revPadding));\n    }\n  };\n}\nfunction unsafeWrapper(fn) {\n  afn(fn);\n  return function (...args) {\n    try {\n      return fn.apply(null, args);\n    } catch (e) {}\n  };\n}\nfunction checksum(len, fn) {\n  anumber(len);\n  afn(fn);\n  return {\n    encode(data) {\n      if (!isBytes(data)) throw new Error('checksum.encode: input should be Uint8Array');\n      const sum = fn(data).slice(0, len);\n      const res = new Uint8Array(data.length + len);\n      res.set(data);\n      res.set(sum, data.length);\n      return res;\n    },\n    decode(data) {\n      if (!isBytes(data)) throw new Error('checksum.decode: input should be Uint8Array');\n      const payload = data.slice(0, -len);\n      const oldChecksum = data.slice(-len);\n      const newChecksum = fn(payload).slice(0, len);\n      for (let i = 0; i < len; i++) if (newChecksum[i] !== oldChecksum[i]) throw new Error('Invalid checksum');\n      return payload;\n    }\n  };\n}\n// prettier-ignore\nexport const utils = {\n  alphabet,\n  chain,\n  checksum,\n  convertRadix,\n  convertRadix2,\n  radix,\n  radix2,\n  join,\n  padding\n};\n// RFC 4648 aka RFC 3548\n// ---------------------\n/**\n * base16 encoding from RFC 4648.\n * @example\n * ```js\n * base16.encode(Uint8Array.from([0x12, 0xab]));\n * // => '12AB'\n * ```\n */\nexport const base16 = chain(radix2(4), alphabet('0123456789ABCDEF'), join(''));\n/**\n * base32 encoding from RFC 4648. Has padding.\n * Use `base32nopad` for unpadded version.\n * Also check out `base32hex`, `base32hexnopad`, `base32crockford`.\n * @example\n * ```js\n * base32.encode(Uint8Array.from([0x12, 0xab]));\n * // => 'CKVQ===='\n * base32.decode('CKVQ====');\n * // => Uint8Array.from([0x12, 0xab])\n * ```\n */\nexport const base32 = chain(radix2(5), alphabet('ABCDEFGHIJKLMNOPQRSTUVWXYZ234567'), padding(5), join(''));\n/**\n * base32 encoding from RFC 4648. No padding.\n * Use `base32` for padded version.\n * Also check out `base32hex`, `base32hexnopad`, `base32crockford`.\n * @example\n * ```js\n * base32nopad.encode(Uint8Array.from([0x12, 0xab]));\n * // => 'CKVQ'\n * base32nopad.decode('CKVQ');\n * // => Uint8Array.from([0x12, 0xab])\n * ```\n */\nexport const base32nopad = chain(radix2(5), alphabet('ABCDEFGHIJKLMNOPQRSTUVWXYZ234567'), join(''));\n/**\n * base32 encoding from RFC 4648. Padded. Compared to ordinary `base32`, slightly different alphabet.\n * Use `base32hexnopad` for unpadded version.\n * @example\n * ```js\n * base32hex.encode(Uint8Array.from([0x12, 0xab]));\n * // => '2ALG===='\n * base32hex.decode('2ALG====');\n * // => Uint8Array.from([0x12, 0xab])\n * ```\n */\nexport const base32hex = chain(radix2(5), alphabet('0123456789ABCDEFGHIJKLMNOPQRSTUV'), padding(5), join(''));\n/**\n * base32 encoding from RFC 4648. No padding. Compared to ordinary `base32`, slightly different alphabet.\n * Use `base32hex` for padded version.\n * @example\n * ```js\n * base32hexnopad.encode(Uint8Array.from([0x12, 0xab]));\n * // => '2ALG'\n * base32hexnopad.decode('2ALG');\n * // => Uint8Array.from([0x12, 0xab])\n * ```\n */\nexport const base32hexnopad = chain(radix2(5), alphabet('0123456789ABCDEFGHIJKLMNOPQRSTUV'), join(''));\n/**\n * base32 encoding from RFC 4648. Doug Crockford's version.\n * https://www.crockford.com/base32.html\n * @example\n * ```js\n * base32crockford.encode(Uint8Array.from([0x12, 0xab]));\n * // => '2ANG'\n * base32crockford.decode('2ANG');\n * // => Uint8Array.from([0x12, 0xab])\n * ```\n */\nexport const base32crockford = chain(radix2(5), alphabet('0123456789ABCDEFGHJKMNPQRSTVWXYZ'), join(''), normalize(s => s.toUpperCase().replace(/O/g, '0').replace(/[IL]/g, '1')));\n// Built-in base64 conversion https://caniuse.com/mdn-javascript_builtins_uint8array_frombase64\n// prettier-ignore\nconst hasBase64Builtin = /* @__PURE__ */(() => typeof Uint8Array.from([]).toBase64 === 'function' && typeof Uint8Array.fromBase64 === 'function')();\nconst decodeBase64Builtin = (s, isUrl) => {\n  astr('base64', s);\n  const re = isUrl ? /^[A-Za-z0-9=_-]+$/ : /^[A-Za-z0-9=+/]+$/;\n  const alphabet = isUrl ? 'base64url' : 'base64';\n  if (s.length > 0 && !re.test(s)) throw new Error('invalid base64');\n  return Uint8Array.fromBase64(s, {\n    alphabet,\n    lastChunkHandling: 'strict'\n  });\n};\n/**\n * base64 from RFC 4648. Padded.\n * Use `base64nopad` for unpadded version.\n * Also check out `base64url`, `base64urlnopad`.\n * Falls back to built-in function, when available.\n * @example\n * ```js\n * base64.encode(Uint8Array.from([0x12, 0xab]));\n * // => 'Eqs='\n * base64.decode('Eqs=');\n * // => Uint8Array.from([0x12, 0xab])\n * ```\n */\n// prettier-ignore\nexport const base64 = hasBase64Builtin ? {\n  encode(b) {\n    abytes(b);\n    return b.toBase64();\n  },\n  decode(s) {\n    return decodeBase64Builtin(s, false);\n  }\n} : chain(radix2(6), alphabet('ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'), padding(6), join(''));\n/**\n * base64 from RFC 4648. No padding.\n * Use `base64` for padded version.\n * @example\n * ```js\n * base64nopad.encode(Uint8Array.from([0x12, 0xab]));\n * // => 'Eqs'\n * base64nopad.decode('Eqs');\n * // => Uint8Array.from([0x12, 0xab])\n * ```\n */\nexport const base64nopad = chain(radix2(6), alphabet('ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'), join(''));\n/**\n * base64 from RFC 4648, using URL-safe alphabet. Padded.\n * Use `base64urlnopad` for unpadded version.\n * Falls back to built-in function, when available.\n * @example\n * ```js\n * base64url.encode(Uint8Array.from([0x12, 0xab]));\n * // => 'Eqs='\n * base64url.decode('Eqs=');\n * // => Uint8Array.from([0x12, 0xab])\n * ```\n */\n// prettier-ignore\nexport const base64url = hasBase64Builtin ? {\n  encode(b) {\n    abytes(b);\n    return b.toBase64({\n      alphabet: 'base64url'\n    });\n  },\n  decode(s) {\n    return decodeBase64Builtin(s, true);\n  }\n} : chain(radix2(6), alphabet('ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_'), padding(6), join(''));\n/**\n * base64 from RFC 4648, using URL-safe alphabet. No padding.\n * Use `base64url` for padded version.\n * @example\n * ```js\n * base64urlnopad.encode(Uint8Array.from([0x12, 0xab]));\n * // => 'Eqs'\n * base64urlnopad.decode('Eqs');\n * // => Uint8Array.from([0x12, 0xab])\n * ```\n */\nexport const base64urlnopad = chain(radix2(6), alphabet('ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_'), join(''));\n// base58 code\n// -----------\nconst genBase58 = /* @__NO_SIDE_EFFECTS__ */abc => chain(radix(58), alphabet(abc), join(''));\n/**\n * base58: base64 without ambigous characters +, /, 0, O, I, l.\n * Quadratic (O(n^2)) - so, can't be used on large inputs.\n * @example\n * ```js\n * base58.decode('01abcdef');\n * // => '3UhJW'\n * ```\n */\nexport const base58 = genBase58('123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz');\n/**\n * base58: flickr version. Check out `base58`.\n */\nexport const base58flickr = genBase58('123456789abcdefghijkmnopqrstuvwxyzABCDEFGHJKLMNPQRSTUVWXYZ');\n/**\n * base58: XRP version. Check out `base58`.\n */\nexport const base58xrp = genBase58('rpshnaf39wBUDNEGHJKLM4PQRST7VWXYZ2bcdeCg65jkm8oFqi1tuvAxyz');\n// Data len (index) -> encoded block len\nconst XMR_BLOCK_LEN = [0, 2, 3, 5, 6, 7, 9, 10, 11];\n/**\n * base58: XMR version. Check out `base58`.\n * Done in 8-byte blocks (which equals 11 chars in decoding). Last (non-full) block padded with '1' to size in XMR_BLOCK_LEN.\n * Block encoding significantly reduces quadratic complexity of base58.\n */\nexport const base58xmr = {\n  encode(data) {\n    let res = '';\n    for (let i = 0; i < data.length; i += 8) {\n      const block = data.subarray(i, i + 8);\n      res += base58.encode(block).padStart(XMR_BLOCK_LEN[block.length], '1');\n    }\n    return res;\n  },\n  decode(str) {\n    let res = [];\n    for (let i = 0; i < str.length; i += 11) {\n      const slice = str.slice(i, i + 11);\n      const blockLen = XMR_BLOCK_LEN.indexOf(slice.length);\n      const block = base58.decode(slice);\n      for (let j = 0; j < block.length - blockLen; j++) {\n        if (block[j] !== 0) throw new Error('base58xmr: wrong padding');\n      }\n      res = res.concat(Array.from(block.slice(block.length - blockLen)));\n    }\n    return Uint8Array.from(res);\n  }\n};\n/**\n * Method, which creates base58check encoder.\n * Requires function, calculating sha256.\n */\nexport const createBase58check = sha256 => chain(checksum(4, data => sha256(sha256(data))), base58);\n/**\n * Use `createBase58check` instead.\n * @deprecated\n */\nexport const base58check = createBase58check;\nconst BECH_ALPHABET = chain(alphabet('qpzry9x8gf2tvdw0s3jn54khce6mua7l'), join(''));\nconst POLYMOD_GENERATORS = [0x3b6a57b2, 0x26508e6d, 0x1ea119fa, 0x3d4233dd, 0x2a1462b3];\nfunction bech32Polymod(pre) {\n  const b = pre >> 25;\n  let chk = (pre & 0x1ffffff) << 5;\n  for (let i = 0; i < POLYMOD_GENERATORS.length; i++) {\n    if ((b >> i & 1) === 1) chk ^= POLYMOD_GENERATORS[i];\n  }\n  return chk;\n}\nfunction bechChecksum(prefix, words, encodingConst = 1) {\n  const len = prefix.length;\n  let chk = 1;\n  for (let i = 0; i < len; i++) {\n    const c = prefix.charCodeAt(i);\n    if (c < 33 || c > 126) throw new Error(`Invalid prefix (${prefix})`);\n    chk = bech32Polymod(chk) ^ c >> 5;\n  }\n  chk = bech32Polymod(chk);\n  for (let i = 0; i < len; i++) chk = bech32Polymod(chk) ^ prefix.charCodeAt(i) & 0x1f;\n  for (let v of words) chk = bech32Polymod(chk) ^ v;\n  for (let i = 0; i < 6; i++) chk = bech32Polymod(chk);\n  chk ^= encodingConst;\n  return BECH_ALPHABET.encode(convertRadix2([chk % powers[30]], 30, 5, false));\n}\n/**\n * @__NO_SIDE_EFFECTS__\n */\nfunction genBech32(encoding) {\n  const ENCODING_CONST = encoding === 'bech32' ? 1 : 0x2bc830a3;\n  const _words = radix2(5);\n  const fromWords = _words.decode;\n  const toWords = _words.encode;\n  const fromWordsUnsafe = unsafeWrapper(fromWords);\n  function encode(prefix, words, limit = 90) {\n    astr('bech32.encode prefix', prefix);\n    if (isBytes(words)) words = Array.from(words);\n    anumArr('bech32.encode', words);\n    const plen = prefix.length;\n    if (plen === 0) throw new TypeError(`Invalid prefix length ${plen}`);\n    const actualLength = plen + 7 + words.length;\n    if (limit !== false && actualLength > limit) throw new TypeError(`Length ${actualLength} exceeds limit ${limit}`);\n    const lowered = prefix.toLowerCase();\n    const sum = bechChecksum(lowered, words, ENCODING_CONST);\n    return `${lowered}1${BECH_ALPHABET.encode(words)}${sum}`;\n  }\n  function decode(str, limit = 90) {\n    astr('bech32.decode input', str);\n    const slen = str.length;\n    if (slen < 8 || limit !== false && slen > limit) throw new TypeError(`invalid string length: ${slen} (${str}). Expected (8..${limit})`);\n    // don't allow mixed case\n    const lowered = str.toLowerCase();\n    if (str !== lowered && str !== str.toUpperCase()) throw new Error(`String must be lowercase or uppercase`);\n    const sepIndex = lowered.lastIndexOf('1');\n    if (sepIndex === 0 || sepIndex === -1) throw new Error(`Letter \"1\" must be present between prefix and data only`);\n    const prefix = lowered.slice(0, sepIndex);\n    const data = lowered.slice(sepIndex + 1);\n    if (data.length < 6) throw new Error('Data must be at least 6 characters long');\n    const words = BECH_ALPHABET.decode(data).slice(0, -6);\n    const sum = bechChecksum(prefix, words, ENCODING_CONST);\n    if (!data.endsWith(sum)) throw new Error(`Invalid checksum in ${str}: expected \"${sum}\"`);\n    return {\n      prefix,\n      words\n    };\n  }\n  const decodeUnsafe = unsafeWrapper(decode);\n  function decodeToBytes(str) {\n    const {\n      prefix,\n      words\n    } = decode(str, false);\n    return {\n      prefix,\n      words,\n      bytes: fromWords(words)\n    };\n  }\n  function encodeFromBytes(prefix, bytes) {\n    return encode(prefix, toWords(bytes));\n  }\n  return {\n    encode,\n    decode,\n    encodeFromBytes,\n    decodeToBytes,\n    decodeUnsafe,\n    fromWords,\n    fromWordsUnsafe,\n    toWords\n  };\n}\n/**\n * bech32 from BIP 173. Operates on words.\n * For high-level, check out scure-btc-signer:\n * https://github.com/paulmillr/scure-btc-signer.\n */\nexport const bech32 = genBech32('bech32');\n/**\n * bech32m from BIP 350. Operates on words.\n * It was to mitigate `bech32` weaknesses.\n * For high-level, check out scure-btc-signer:\n * https://github.com/paulmillr/scure-btc-signer.\n */\nexport const bech32m = genBech32('bech32m');\n/**\n * UTF-8-to-byte decoder. Uses built-in TextDecoder / TextEncoder.\n * @example\n * ```js\n * const b = utf8.decode(\"hey\"); // => new Uint8Array([ 104, 101, 121 ])\n * const str = utf8.encode(b); // \"hey\"\n * ```\n */\nexport const utf8 = {\n  encode: data => new TextDecoder().decode(data),\n  decode: str => new TextEncoder().encode(str)\n};\n// Built-in hex conversion https://caniuse.com/mdn-javascript_builtins_uint8array_fromhex\n// prettier-ignore\nconst hasHexBuiltin = /* @__PURE__ */(() => typeof Uint8Array.from([]).toHex === 'function' && typeof Uint8Array.fromHex === 'function')();\n// prettier-ignore\nconst hexBuiltin = {\n  encode(data) {\n    abytes(data);\n    return data.toHex();\n  },\n  decode(s) {\n    astr('hex', s);\n    return Uint8Array.fromHex(s);\n  }\n};\n/**\n * hex string decoder. Uses built-in function, when available.\n * @example\n * ```js\n * const b = hex.decode(\"0102ff\"); // => new Uint8Array([ 1, 2, 255 ])\n * const str = hex.encode(b); // \"0102ff\"\n * ```\n */\nexport const hex = hasHexBuiltin ? hexBuiltin : chain(radix2(4), alphabet('0123456789abcdef'), join(''), normalize(s => {\n  if (typeof s !== 'string' || s.length % 2 !== 0) throw new TypeError(`hex.decode: expected string, got ${typeof s} with length ${s.length}`);\n  return s.toLowerCase();\n}));\n// prettier-ignore\nconst CODERS = {\n  utf8,\n  hex,\n  base16,\n  base32,\n  base64,\n  base64url,\n  base58,\n  base58xmr\n};\nconst coderTypeError = 'Invalid encoding type. Available types: utf8, hex, base16, base32, base64, base64url, base58, base58xmr';\n/** @deprecated */\nexport const bytesToString = (type, bytes) => {\n  if (typeof type !== 'string' || !CODERS.hasOwnProperty(type)) throw new TypeError(coderTypeError);\n  if (!isBytes(bytes)) throw new TypeError('bytesToString() expects Uint8Array');\n  return CODERS[type].encode(bytes);\n};\n/** @deprecated */\nexport const str = bytesToString; // as in python, but for bytes only\n/** @deprecated */\nexport const stringToBytes = (type, str) => {\n  if (!CODERS.hasOwnProperty(type)) throw new TypeError(coderTypeError);\n  if (typeof str !== 'string') throw new TypeError('stringToBytes() expects string');\n  return CODERS[type].decode(str);\n};\n/** @deprecated */\nexport const bytes = stringToBytes;", "map": {"version": 3, "names": ["isBytes", "a", "Uint8Array", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "constructor", "name", "abytes", "b", "lengths", "Error", "length", "includes", "isArrayOf", "isString", "arr", "Array", "isArray", "every", "item", "Number", "isSafeInteger", "afn", "input", "astr", "label", "anumber", "n", "aArr", "astrArr", "anum<PERSON>rr", "chain", "args", "id", "wrap", "c", "encode", "map", "x", "reduceRight", "decode", "reduce", "alphabet", "letters", "lettersA", "split", "len", "indexes", "Map", "l", "i", "digits", "letter", "get", "undefined", "join", "separator", "from", "to", "padding", "bits", "chr", "data", "push", "end", "last", "byte", "slice", "normalize", "fn", "convertRadix", "pos", "res", "d", "dlen", "carry", "done", "digit", "fromCarry", "digitBase", "div", "rounded", "Math", "floor", "reverse", "gcd", "radix2carry", "powers", "convertRadix2", "max", "mask", "pow", "radix", "num", "_256", "bytes", "radix2", "revPadding", "unsafeWrapper", "apply", "e", "checksum", "sum", "set", "payload", "oldChecksum", "newChe<PERSON><PERSON>", "utils", "base16", "base32", "base32nopad", "base32hex", "base32hexnopad", "base32crockford", "s", "toUpperCase", "replace", "hasBase64Builtin", "toBase64", "fromBase64", "decodeBase64Builtin", "isUrl", "re", "test", "lastChunkHandling", "base64", "base64nopad", "base64url", "base64urlnopad", "genBase58", "abc", "base58", "base58flickr", "base58xrp", "XMR_BLOCK_LEN", "base58xmr", "block", "subarray", "padStart", "str", "blockLen", "indexOf", "j", "concat", "createBase58check", "sha256", "base58check", "BECH_ALPHABET", "POLYMOD_GENERATORS", "bech32Polymod", "pre", "chk", "bechChecksum", "prefix", "words", "encodingConst", "charCodeAt", "v", "genBech32", "encoding", "ENCODING_CONST", "_words", "fromWords", "to<PERSON><PERSON>s", "fromWordsUnsafe", "limit", "plen", "TypeError", "actualLength", "lowered", "toLowerCase", "slen", "sepIndex", "lastIndexOf", "endsWith", "decodeUnsafe", "decodeToBytes", "encodeFromBytes", "bech32", "bech32m", "utf8", "TextDecoder", "TextEncoder", "hasHexBuiltin", "toHex", "fromHex", "hexBuiltin", "hex", "CODERS", "coderTypeError", "bytesToString", "type", "hasOwnProperty", "stringToBytes"], "sources": ["C:\\Users\\<USER>\\Desktop\\Aptos\\freelancer-escrow-frontend\\node_modules\\@scure\\base\\index.ts"], "sourcesContent": ["/*! scure-base - MIT License (c) 2022 <PERSON> (paulmillr.com) */\n\nexport interface Coder<F, T> {\n  encode(from: F): T;\n  decode(to: T): F;\n}\n\nexport interface BytesCoder extends Coder<Uint8Array, string> {\n  encode: (data: Uint8Array) => string;\n  decode: (str: string) => Uint8Array;\n}\n\nfunction isBytes(a: unknown): a is Uint8Array {\n  return a instanceof Uint8Array || (ArrayBuffer.isView(a) && a.constructor.name === 'Uint8Array');\n}\n/** Asserts something is Uint8Array. */\nfunction abytes(b: Uint8Array | undefined, ...lengths: number[]): void {\n  if (!isBytes(b)) throw new Error('Uint8Array expected');\n  if (lengths.length > 0 && !lengths.includes(b.length))\n    throw new Error('Uint8Array expected of length ' + lengths + ', got length=' + b.length);\n}\n\nfunction isArrayOf(isString: boolean, arr: any[]) {\n  if (!Array.isArray(arr)) return false;\n  if (arr.length === 0) return true;\n  if (isString) {\n    return arr.every((item) => typeof item === 'string');\n  } else {\n    return arr.every((item) => Number.isSafeInteger(item));\n  }\n}\n\n// no abytes: seems to have 10% slowdown. Why?!\n\nfunction afn(input: Function): input is Function {\n  if (typeof input !== 'function') throw new Error('function expected');\n  return true;\n}\n\nfunction astr(label: string, input: unknown): input is string {\n  if (typeof input !== 'string') throw new Error(`${label}: string expected`);\n  return true;\n}\n\nfunction anumber(n: number): void {\n  if (!Number.isSafeInteger(n)) throw new Error(`invalid integer: ${n}`);\n}\n\nfunction aArr(input: any[]) {\n  if (!Array.isArray(input)) throw new Error('array expected');\n}\nfunction astrArr(label: string, input: string[]) {\n  if (!isArrayOf(true, input)) throw new Error(`${label}: array of strings expected`);\n}\nfunction anumArr(label: string, input: number[]) {\n  if (!isArrayOf(false, input)) throw new Error(`${label}: array of numbers expected`);\n}\n\n// TODO: some recusive type inference so it would check correct order of input/output inside rest?\n// like <string, number>, <number, bytes>, <bytes, float>\ntype Chain = [Coder<any, any>, ...Coder<any, any>[]];\n// Extract info from Coder type\ntype Input<F> = F extends Coder<infer T, any> ? T : never;\ntype Output<F> = F extends Coder<any, infer T> ? T : never;\n// Generic function for arrays\ntype First<T> = T extends [infer U, ...any[]] ? U : never;\ntype Last<T> = T extends [...any[], infer U] ? U : never;\ntype Tail<T> = T extends [any, ...infer U] ? U : never;\n\ntype AsChain<C extends Chain, Rest = Tail<C>> = {\n  // C[K] = Coder<Input<C[K]>, Input<Rest[k]>>\n  [K in keyof C]: Coder<Input<C[K]>, Input<K extends keyof Rest ? Rest[K] : any>>;\n};\n\n/**\n * @__NO_SIDE_EFFECTS__\n */\nfunction chain<T extends Chain & AsChain<T>>(...args: T): Coder<Input<First<T>>, Output<Last<T>>> {\n  const id = (a: any) => a;\n  // Wrap call in closure so JIT can inline calls\n  const wrap = (a: any, b: any) => (c: any) => a(b(c));\n  // Construct chain of args[-1].encode(args[-2].encode([...]))\n  const encode = args.map((x) => x.encode).reduceRight(wrap, id);\n  // Construct chain of args[0].decode(args[1].decode(...))\n  const decode = args.map((x) => x.decode).reduce(wrap, id);\n  return { encode, decode };\n}\n\n/**\n * Encodes integer radix representation to array of strings using alphabet and back.\n * Could also be array of strings.\n * @__NO_SIDE_EFFECTS__\n */\nfunction alphabet(letters: string | string[]): Coder<number[], string[]> {\n  // mapping 1 to \"b\"\n  const lettersA = typeof letters === 'string' ? letters.split('') : letters;\n  const len = lettersA.length;\n  astrArr('alphabet', lettersA);\n\n  // mapping \"b\" to 1\n  const indexes = new Map(lettersA.map((l, i) => [l, i]));\n  return {\n    encode: (digits: number[]) => {\n      aArr(digits);\n      return digits.map((i) => {\n        if (!Number.isSafeInteger(i) || i < 0 || i >= len)\n          throw new Error(\n            `alphabet.encode: digit index outside alphabet \"${i}\". Allowed: ${letters}`\n          );\n        return lettersA[i]!;\n      });\n    },\n    decode: (input: string[]): number[] => {\n      aArr(input);\n      return input.map((letter) => {\n        astr('alphabet.decode', letter);\n        const i = indexes.get(letter);\n        if (i === undefined) throw new Error(`Unknown letter: \"${letter}\". Allowed: ${letters}`);\n        return i;\n      });\n    },\n  };\n}\n\n/**\n * @__NO_SIDE_EFFECTS__\n */\nfunction join(separator = ''): Coder<string[], string> {\n  astr('join', separator);\n  return {\n    encode: (from) => {\n      astrArr('join.decode', from);\n      return from.join(separator);\n    },\n    decode: (to) => {\n      astr('join.decode', to);\n      return to.split(separator);\n    },\n  };\n}\n\n/**\n * Pad strings array so it has integer number of bits\n * @__NO_SIDE_EFFECTS__\n */\nfunction padding(bits: number, chr = '='): Coder<string[], string[]> {\n  anumber(bits);\n  astr('padding', chr);\n  return {\n    encode(data: string[]): string[] {\n      astrArr('padding.encode', data);\n      while ((data.length * bits) % 8) data.push(chr);\n      return data;\n    },\n    decode(input: string[]): string[] {\n      astrArr('padding.decode', input);\n      let end = input.length;\n      if ((end * bits) % 8)\n        throw new Error('padding: invalid, string should have whole number of bytes');\n      for (; end > 0 && input[end - 1] === chr; end--) {\n        const last = end - 1;\n        const byte = last * bits;\n        if (byte % 8 === 0) throw new Error('padding: invalid, string has too much padding');\n      }\n      return input.slice(0, end);\n    },\n  };\n}\n\n/**\n * @__NO_SIDE_EFFECTS__\n */\nfunction normalize<T>(fn: (val: T) => T): Coder<T, T> {\n  afn(fn);\n  return { encode: (from: T) => from, decode: (to: T) => fn(to) };\n}\n\n/**\n * Slow: O(n^2) time complexity\n */\nfunction convertRadix(data: number[], from: number, to: number): number[] {\n  // base 1 is impossible\n  if (from < 2) throw new Error(`convertRadix: invalid from=${from}, base cannot be less than 2`);\n  if (to < 2) throw new Error(`convertRadix: invalid to=${to}, base cannot be less than 2`);\n  aArr(data);\n  if (!data.length) return [];\n  let pos = 0;\n  const res = [];\n  const digits = Array.from(data, (d) => {\n    anumber(d);\n    if (d < 0 || d >= from) throw new Error(`invalid integer: ${d}`);\n    return d;\n  });\n  const dlen = digits.length;\n  while (true) {\n    let carry = 0;\n    let done = true;\n    for (let i = pos; i < dlen; i++) {\n      const digit = digits[i]!;\n      const fromCarry = from * carry;\n      const digitBase = fromCarry + digit;\n      if (\n        !Number.isSafeInteger(digitBase) ||\n        fromCarry / from !== carry ||\n        digitBase - digit !== fromCarry\n      ) {\n        throw new Error('convertRadix: carry overflow');\n      }\n      const div = digitBase / to;\n      carry = digitBase % to;\n      const rounded = Math.floor(div);\n      digits[i] = rounded;\n      if (!Number.isSafeInteger(rounded) || rounded * to + carry !== digitBase)\n        throw new Error('convertRadix: carry overflow');\n      if (!done) continue;\n      else if (!rounded) pos = i;\n      else done = false;\n    }\n    res.push(carry);\n    if (done) break;\n  }\n  for (let i = 0; i < data.length - 1 && data[i] === 0; i++) res.push(0);\n  return res.reverse();\n}\n\nconst gcd = (a: number, b: number): number => (b === 0 ? a : gcd(b, a % b));\nconst radix2carry = /* @__NO_SIDE_EFFECTS__ */ (from: number, to: number) =>\n  from + (to - gcd(from, to));\nconst powers: number[] = /* @__PURE__ */ (() => {\n  let res = [];\n  for (let i = 0; i < 40; i++) res.push(2 ** i);\n  return res;\n})();\n/**\n * Implemented with numbers, because BigInt is 5x slower\n */\nfunction convertRadix2(data: number[], from: number, to: number, padding: boolean): number[] {\n  aArr(data);\n  if (from <= 0 || from > 32) throw new Error(`convertRadix2: wrong from=${from}`);\n  if (to <= 0 || to > 32) throw new Error(`convertRadix2: wrong to=${to}`);\n  if (radix2carry(from, to) > 32) {\n    throw new Error(\n      `convertRadix2: carry overflow from=${from} to=${to} carryBits=${radix2carry(from, to)}`\n    );\n  }\n  let carry = 0;\n  let pos = 0; // bitwise position in current element\n  const max = powers[from]!;\n  const mask = powers[to]! - 1;\n  const res: number[] = [];\n  for (const n of data) {\n    anumber(n);\n    if (n >= max) throw new Error(`convertRadix2: invalid data word=${n} from=${from}`);\n    carry = (carry << from) | n;\n    if (pos + from > 32) throw new Error(`convertRadix2: carry overflow pos=${pos} from=${from}`);\n    pos += from;\n    for (; pos >= to; pos -= to) res.push(((carry >> (pos - to)) & mask) >>> 0);\n    const pow = powers[pos];\n    if (pow === undefined) throw new Error('invalid carry');\n    carry &= pow - 1; // clean carry, otherwise it will cause overflow\n  }\n  carry = (carry << (to - pos)) & mask;\n  if (!padding && pos >= from) throw new Error('Excess padding');\n  if (!padding && carry > 0) throw new Error(`Non-zero padding: ${carry}`);\n  if (padding && pos > 0) res.push(carry >>> 0);\n  return res;\n}\n\n/**\n * @__NO_SIDE_EFFECTS__\n */\nfunction radix(num: number): Coder<Uint8Array, number[]> {\n  anumber(num);\n  const _256 = 2 ** 8;\n  return {\n    encode: (bytes: Uint8Array) => {\n      if (!isBytes(bytes)) throw new Error('radix.encode input should be Uint8Array');\n      return convertRadix(Array.from(bytes), _256, num);\n    },\n    decode: (digits: number[]) => {\n      anumArr('radix.decode', digits);\n      return Uint8Array.from(convertRadix(digits, num, _256));\n    },\n  };\n}\n\n/**\n * If both bases are power of same number (like `2**8 <-> 2**64`),\n * there is a linear algorithm. For now we have implementation for power-of-two bases only.\n * @__NO_SIDE_EFFECTS__\n */\nfunction radix2(bits: number, revPadding = false): Coder<Uint8Array, number[]> {\n  anumber(bits);\n  if (bits <= 0 || bits > 32) throw new Error('radix2: bits should be in (0..32]');\n  if (radix2carry(8, bits) > 32 || radix2carry(bits, 8) > 32)\n    throw new Error('radix2: carry overflow');\n  return {\n    encode: (bytes: Uint8Array) => {\n      if (!isBytes(bytes)) throw new Error('radix2.encode input should be Uint8Array');\n      return convertRadix2(Array.from(bytes), 8, bits, !revPadding);\n    },\n    decode: (digits: number[]) => {\n      anumArr('radix2.decode', digits);\n      return Uint8Array.from(convertRadix2(digits, bits, 8, revPadding));\n    },\n  };\n}\n\ntype ArgumentTypes<F extends Function> = F extends (...args: infer A) => any ? A : never;\nfunction unsafeWrapper<T extends (...args: any) => any>(fn: T) {\n  afn(fn);\n  return function (...args: ArgumentTypes<T>): ReturnType<T> | void {\n    try {\n      return fn.apply(null, args);\n    } catch (e) {}\n  };\n}\n\nfunction checksum(\n  len: number,\n  fn: (data: Uint8Array) => Uint8Array\n): Coder<Uint8Array, Uint8Array> {\n  anumber(len);\n  afn(fn);\n  return {\n    encode(data: Uint8Array) {\n      if (!isBytes(data)) throw new Error('checksum.encode: input should be Uint8Array');\n      const sum = fn(data).slice(0, len);\n      const res = new Uint8Array(data.length + len);\n      res.set(data);\n      res.set(sum, data.length);\n      return res;\n    },\n    decode(data: Uint8Array) {\n      if (!isBytes(data)) throw new Error('checksum.decode: input should be Uint8Array');\n      const payload = data.slice(0, -len);\n      const oldChecksum = data.slice(-len);\n      const newChecksum = fn(payload).slice(0, len);\n      for (let i = 0; i < len; i++)\n        if (newChecksum[i] !== oldChecksum[i]) throw new Error('Invalid checksum');\n      return payload;\n    },\n  };\n}\n\n// prettier-ignore\nexport const utils: { alphabet: typeof alphabet; chain: typeof chain; checksum: typeof checksum; convertRadix: typeof convertRadix; convertRadix2: typeof convertRadix2; radix: typeof radix; radix2: typeof radix2; join: typeof join; padding: typeof padding; } = {\n  alphabet, chain, checksum, convertRadix, convertRadix2, radix, radix2, join, padding,\n};\n\n// RFC 4648 aka RFC 3548\n// ---------------------\n\n/**\n * base16 encoding from RFC 4648.\n * @example\n * ```js\n * base16.encode(Uint8Array.from([0x12, 0xab]));\n * // => '12AB'\n * ```\n */\nexport const base16: BytesCoder = chain(radix2(4), alphabet('0123456789ABCDEF'), join(''));\n\n/**\n * base32 encoding from RFC 4648. Has padding.\n * Use `base32nopad` for unpadded version.\n * Also check out `base32hex`, `base32hexnopad`, `base32crockford`.\n * @example\n * ```js\n * base32.encode(Uint8Array.from([0x12, 0xab]));\n * // => 'CKVQ===='\n * base32.decode('CKVQ====');\n * // => Uint8Array.from([0x12, 0xab])\n * ```\n */\nexport const base32: BytesCoder = chain(\n  radix2(5),\n  alphabet('ABCDEFGHIJKLMNOPQRSTUVWXYZ234567'),\n  padding(5),\n  join('')\n);\n\n/**\n * base32 encoding from RFC 4648. No padding.\n * Use `base32` for padded version.\n * Also check out `base32hex`, `base32hexnopad`, `base32crockford`.\n * @example\n * ```js\n * base32nopad.encode(Uint8Array.from([0x12, 0xab]));\n * // => 'CKVQ'\n * base32nopad.decode('CKVQ');\n * // => Uint8Array.from([0x12, 0xab])\n * ```\n */\nexport const base32nopad: BytesCoder = chain(\n  radix2(5),\n  alphabet('ABCDEFGHIJKLMNOPQRSTUVWXYZ234567'),\n  join('')\n);\n/**\n * base32 encoding from RFC 4648. Padded. Compared to ordinary `base32`, slightly different alphabet.\n * Use `base32hexnopad` for unpadded version.\n * @example\n * ```js\n * base32hex.encode(Uint8Array.from([0x12, 0xab]));\n * // => '2ALG===='\n * base32hex.decode('2ALG====');\n * // => Uint8Array.from([0x12, 0xab])\n * ```\n */\nexport const base32hex: BytesCoder = chain(\n  radix2(5),\n  alphabet('0123456789ABCDEFGHIJKLMNOPQRSTUV'),\n  padding(5),\n  join('')\n);\n\n/**\n * base32 encoding from RFC 4648. No padding. Compared to ordinary `base32`, slightly different alphabet.\n * Use `base32hex` for padded version.\n * @example\n * ```js\n * base32hexnopad.encode(Uint8Array.from([0x12, 0xab]));\n * // => '2ALG'\n * base32hexnopad.decode('2ALG');\n * // => Uint8Array.from([0x12, 0xab])\n * ```\n */\nexport const base32hexnopad: BytesCoder = chain(\n  radix2(5),\n  alphabet('0123456789ABCDEFGHIJKLMNOPQRSTUV'),\n  join('')\n);\n/**\n * base32 encoding from RFC 4648. Doug Crockford's version.\n * https://www.crockford.com/base32.html\n * @example\n * ```js\n * base32crockford.encode(Uint8Array.from([0x12, 0xab]));\n * // => '2ANG'\n * base32crockford.decode('2ANG');\n * // => Uint8Array.from([0x12, 0xab])\n * ```\n */\nexport const base32crockford: BytesCoder = chain(\n  radix2(5),\n  alphabet('0123456789ABCDEFGHJKMNPQRSTVWXYZ'),\n  join(''),\n  normalize((s: string) => s.toUpperCase().replace(/O/g, '0').replace(/[IL]/g, '1'))\n);\n\n// Built-in base64 conversion https://caniuse.com/mdn-javascript_builtins_uint8array_frombase64\n// prettier-ignore\nconst hasBase64Builtin: boolean = /* @__PURE__ */ (() =>\n  typeof (Uint8Array as any).from([]).toBase64 === 'function' &&\n  typeof (Uint8Array as any).fromBase64 === 'function')();\n\nconst decodeBase64Builtin = (s: string, isUrl: boolean) => {\n  astr('base64', s);\n  const re = isUrl ? /^[A-Za-z0-9=_-]+$/ : /^[A-Za-z0-9=+/]+$/;\n  const alphabet = isUrl ? 'base64url' : 'base64';\n  if (s.length > 0 && !re.test(s)) throw new Error('invalid base64');\n  return (Uint8Array as any).fromBase64(s, { alphabet, lastChunkHandling: 'strict' });\n};\n\n/**\n * base64 from RFC 4648. Padded.\n * Use `base64nopad` for unpadded version.\n * Also check out `base64url`, `base64urlnopad`.\n * Falls back to built-in function, when available.\n * @example\n * ```js\n * base64.encode(Uint8Array.from([0x12, 0xab]));\n * // => 'Eqs='\n * base64.decode('Eqs=');\n * // => Uint8Array.from([0x12, 0xab])\n * ```\n */\n// prettier-ignore\nexport const base64: BytesCoder = hasBase64Builtin ? {\n  encode(b) { abytes(b); return (b as any).toBase64(); },\n  decode(s) { return decodeBase64Builtin(s, false); },\n} : chain(\n  radix2(6),\n  alphabet('ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'),\n  padding(6),\n  join('')\n);\n/**\n * base64 from RFC 4648. No padding.\n * Use `base64` for padded version.\n * @example\n * ```js\n * base64nopad.encode(Uint8Array.from([0x12, 0xab]));\n * // => 'Eqs'\n * base64nopad.decode('Eqs');\n * // => Uint8Array.from([0x12, 0xab])\n * ```\n */\nexport const base64nopad: BytesCoder = chain(\n  radix2(6),\n  alphabet('ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'),\n  join('')\n);\n\n/**\n * base64 from RFC 4648, using URL-safe alphabet. Padded.\n * Use `base64urlnopad` for unpadded version.\n * Falls back to built-in function, when available.\n * @example\n * ```js\n * base64url.encode(Uint8Array.from([0x12, 0xab]));\n * // => 'Eqs='\n * base64url.decode('Eqs=');\n * // => Uint8Array.from([0x12, 0xab])\n * ```\n */\n// prettier-ignore\nexport const base64url: BytesCoder = hasBase64Builtin ? {\n  encode(b) { abytes(b); return (b as any).toBase64({ alphabet: 'base64url' }); },\n  decode(s) { return decodeBase64Builtin(s, true); },\n} : chain(\n  radix2(6),\n  alphabet('ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_'),\n  padding(6),\n  join('')\n);\n\n/**\n * base64 from RFC 4648, using URL-safe alphabet. No padding.\n * Use `base64url` for padded version.\n * @example\n * ```js\n * base64urlnopad.encode(Uint8Array.from([0x12, 0xab]));\n * // => 'Eqs'\n * base64urlnopad.decode('Eqs');\n * // => Uint8Array.from([0x12, 0xab])\n * ```\n */\nexport const base64urlnopad: BytesCoder = chain(\n  radix2(6),\n  alphabet('ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_'),\n  join('')\n);\n\n// base58 code\n// -----------\nconst genBase58 = /* @__NO_SIDE_EFFECTS__ */ (abc: string) =>\n  chain(radix(58), alphabet(abc), join(''));\n\n/**\n * base58: base64 without ambigous characters +, /, 0, O, I, l.\n * Quadratic (O(n^2)) - so, can't be used on large inputs.\n * @example\n * ```js\n * base58.decode('01abcdef');\n * // => '3UhJW'\n * ```\n */\nexport const base58: BytesCoder = genBase58(\n  '123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz'\n);\n/**\n * base58: flickr version. Check out `base58`.\n */\nexport const base58flickr: BytesCoder = genBase58(\n  '123456789abcdefghijkmnopqrstuvwxyzABCDEFGHJKLMNPQRSTUVWXYZ'\n);\n/**\n * base58: XRP version. Check out `base58`.\n */\nexport const base58xrp: BytesCoder = genBase58(\n  'rpshnaf39wBUDNEGHJKLM4PQRST7VWXYZ2bcdeCg65jkm8oFqi1tuvAxyz'\n);\n\n// Data len (index) -> encoded block len\nconst XMR_BLOCK_LEN = [0, 2, 3, 5, 6, 7, 9, 10, 11];\n\n/**\n * base58: XMR version. Check out `base58`.\n * Done in 8-byte blocks (which equals 11 chars in decoding). Last (non-full) block padded with '1' to size in XMR_BLOCK_LEN.\n * Block encoding significantly reduces quadratic complexity of base58.\n */\nexport const base58xmr: BytesCoder = {\n  encode(data: Uint8Array) {\n    let res = '';\n    for (let i = 0; i < data.length; i += 8) {\n      const block = data.subarray(i, i + 8);\n      res += base58.encode(block).padStart(XMR_BLOCK_LEN[block.length]!, '1');\n    }\n    return res;\n  },\n  decode(str: string) {\n    let res: number[] = [];\n    for (let i = 0; i < str.length; i += 11) {\n      const slice = str.slice(i, i + 11);\n      const blockLen = XMR_BLOCK_LEN.indexOf(slice.length);\n      const block = base58.decode(slice);\n      for (let j = 0; j < block.length - blockLen; j++) {\n        if (block[j] !== 0) throw new Error('base58xmr: wrong padding');\n      }\n      res = res.concat(Array.from(block.slice(block.length - blockLen)));\n    }\n    return Uint8Array.from(res);\n  },\n};\n\n/**\n * Method, which creates base58check encoder.\n * Requires function, calculating sha256.\n */\nexport const createBase58check = (sha256: (data: Uint8Array) => Uint8Array): BytesCoder =>\n  chain(\n    checksum(4, (data) => sha256(sha256(data))),\n    base58\n  );\n\n/**\n * Use `createBase58check` instead.\n * @deprecated\n */\nexport const base58check: (sha256: (data: Uint8Array) => Uint8Array) => BytesCoder =\n  createBase58check;\n\n// Bech32 code\n// -----------\nexport interface Bech32Decoded<Prefix extends string = string> {\n  prefix: Prefix;\n  words: number[];\n}\nexport interface Bech32DecodedWithArray<Prefix extends string = string> {\n  prefix: Prefix;\n  words: number[];\n  bytes: Uint8Array;\n}\n\nconst BECH_ALPHABET: Coder<number[], string> = chain(\n  alphabet('qpzry9x8gf2tvdw0s3jn54khce6mua7l'),\n  join('')\n);\n\nconst POLYMOD_GENERATORS = [0x3b6a57b2, 0x26508e6d, 0x1ea119fa, 0x3d4233dd, 0x2a1462b3];\nfunction bech32Polymod(pre: number): number {\n  const b = pre >> 25;\n  let chk = (pre & 0x1ffffff) << 5;\n  for (let i = 0; i < POLYMOD_GENERATORS.length; i++) {\n    if (((b >> i) & 1) === 1) chk ^= POLYMOD_GENERATORS[i]!;\n  }\n  return chk;\n}\n\nfunction bechChecksum(prefix: string, words: number[], encodingConst = 1): string {\n  const len = prefix.length;\n  let chk = 1;\n  for (let i = 0; i < len; i++) {\n    const c = prefix.charCodeAt(i);\n    if (c < 33 || c > 126) throw new Error(`Invalid prefix (${prefix})`);\n    chk = bech32Polymod(chk) ^ (c >> 5);\n  }\n  chk = bech32Polymod(chk);\n  for (let i = 0; i < len; i++) chk = bech32Polymod(chk) ^ (prefix.charCodeAt(i) & 0x1f);\n  for (let v of words) chk = bech32Polymod(chk) ^ v;\n  for (let i = 0; i < 6; i++) chk = bech32Polymod(chk);\n  chk ^= encodingConst;\n  return BECH_ALPHABET.encode(convertRadix2([chk % powers[30]!], 30, 5, false));\n}\n\nexport interface Bech32 {\n  encode<Prefix extends string>(\n    prefix: Prefix,\n    words: number[] | Uint8Array,\n    limit?: number | false\n  ): `${Lowercase<Prefix>}1${string}`;\n  decode<Prefix extends string>(\n    str: `${Prefix}1${string}`,\n    limit?: number | false\n  ): Bech32Decoded<Prefix>;\n  encodeFromBytes(prefix: string, bytes: Uint8Array): string;\n  decodeToBytes(str: string): Bech32DecodedWithArray;\n  decodeUnsafe(str: string, limit?: number | false): void | Bech32Decoded<string>;\n  fromWords(to: number[]): Uint8Array;\n  fromWordsUnsafe(to: number[]): void | Uint8Array;\n  toWords(from: Uint8Array): number[];\n}\n/**\n * @__NO_SIDE_EFFECTS__\n */\nfunction genBech32(encoding: 'bech32' | 'bech32m'): Bech32 {\n  const ENCODING_CONST = encoding === 'bech32' ? 1 : 0x2bc830a3;\n  const _words = radix2(5);\n  const fromWords = _words.decode;\n  const toWords = _words.encode;\n  const fromWordsUnsafe = unsafeWrapper(fromWords);\n\n  function encode<Prefix extends string>(\n    prefix: Prefix,\n    words: number[] | Uint8Array,\n    limit: number | false = 90\n  ): `${Lowercase<Prefix>}1${string}` {\n    astr('bech32.encode prefix', prefix);\n    if (isBytes(words)) words = Array.from(words);\n    anumArr('bech32.encode', words);\n    const plen = prefix.length;\n    if (plen === 0) throw new TypeError(`Invalid prefix length ${plen}`);\n    const actualLength = plen + 7 + words.length;\n    if (limit !== false && actualLength > limit)\n      throw new TypeError(`Length ${actualLength} exceeds limit ${limit}`);\n    const lowered = prefix.toLowerCase();\n    const sum = bechChecksum(lowered, words, ENCODING_CONST);\n    return `${lowered}1${BECH_ALPHABET.encode(words)}${sum}` as `${Lowercase<Prefix>}1${string}`;\n  }\n\n  function decode<Prefix extends string>(\n    str: `${Prefix}1${string}`,\n    limit?: number | false\n  ): Bech32Decoded<Prefix>;\n  function decode(str: string, limit?: number | false): Bech32Decoded;\n  function decode(str: string, limit: number | false = 90): Bech32Decoded {\n    astr('bech32.decode input', str);\n    const slen = str.length;\n    if (slen < 8 || (limit !== false && slen > limit))\n      throw new TypeError(`invalid string length: ${slen} (${str}). Expected (8..${limit})`);\n    // don't allow mixed case\n    const lowered = str.toLowerCase();\n    if (str !== lowered && str !== str.toUpperCase())\n      throw new Error(`String must be lowercase or uppercase`);\n    const sepIndex = lowered.lastIndexOf('1');\n    if (sepIndex === 0 || sepIndex === -1)\n      throw new Error(`Letter \"1\" must be present between prefix and data only`);\n    const prefix = lowered.slice(0, sepIndex);\n    const data = lowered.slice(sepIndex + 1);\n    if (data.length < 6) throw new Error('Data must be at least 6 characters long');\n    const words = BECH_ALPHABET.decode(data).slice(0, -6);\n    const sum = bechChecksum(prefix, words, ENCODING_CONST);\n    if (!data.endsWith(sum)) throw new Error(`Invalid checksum in ${str}: expected \"${sum}\"`);\n    return { prefix, words };\n  }\n\n  const decodeUnsafe = unsafeWrapper(decode);\n\n  function decodeToBytes(str: string): Bech32DecodedWithArray {\n    const { prefix, words } = decode(str, false);\n    return { prefix, words, bytes: fromWords(words) };\n  }\n\n  function encodeFromBytes(prefix: string, bytes: Uint8Array) {\n    return encode(prefix, toWords(bytes));\n  }\n\n  return {\n    encode,\n    decode,\n    encodeFromBytes,\n    decodeToBytes,\n    decodeUnsafe,\n    fromWords,\n    fromWordsUnsafe,\n    toWords,\n  };\n}\n\n/**\n * bech32 from BIP 173. Operates on words.\n * For high-level, check out scure-btc-signer:\n * https://github.com/paulmillr/scure-btc-signer.\n */\nexport const bech32: Bech32 = genBech32('bech32');\n\n/**\n * bech32m from BIP 350. Operates on words.\n * It was to mitigate `bech32` weaknesses.\n * For high-level, check out scure-btc-signer:\n * https://github.com/paulmillr/scure-btc-signer.\n */\nexport const bech32m: Bech32 = genBech32('bech32m');\n\ndeclare const TextEncoder: any;\ndeclare const TextDecoder: any;\n\n/**\n * UTF-8-to-byte decoder. Uses built-in TextDecoder / TextEncoder.\n * @example\n * ```js\n * const b = utf8.decode(\"hey\"); // => new Uint8Array([ 104, 101, 121 ])\n * const str = utf8.encode(b); // \"hey\"\n * ```\n */\nexport const utf8: BytesCoder = {\n  encode: (data) => new TextDecoder().decode(data),\n  decode: (str) => new TextEncoder().encode(str),\n};\n\n// Built-in hex conversion https://caniuse.com/mdn-javascript_builtins_uint8array_fromhex\n// prettier-ignore\nconst hasHexBuiltin: boolean = /* @__PURE__ */ (() =>\n  typeof (Uint8Array as any).from([]).toHex === 'function' &&\n  typeof (Uint8Array as any).fromHex === 'function')();\n// prettier-ignore\nconst hexBuiltin: BytesCoder = {\n  encode(data) { abytes(data); return (data as any).toHex(); },\n  decode(s) { astr('hex', s); return (Uint8Array as any).fromHex(s); },\n};\n/**\n * hex string decoder. Uses built-in function, when available.\n * @example\n * ```js\n * const b = hex.decode(\"0102ff\"); // => new Uint8Array([ 1, 2, 255 ])\n * const str = hex.encode(b); // \"0102ff\"\n * ```\n */\nexport const hex: BytesCoder = hasHexBuiltin\n  ? hexBuiltin\n  : chain(\n      radix2(4),\n      alphabet('0123456789abcdef'),\n      join(''),\n      normalize((s: string) => {\n        if (typeof s !== 'string' || s.length % 2 !== 0)\n          throw new TypeError(\n            `hex.decode: expected string, got ${typeof s} with length ${s.length}`\n          );\n        return s.toLowerCase();\n      })\n    );\n\nexport type SomeCoders = {\n  utf8: BytesCoder;\n  hex: BytesCoder;\n  base16: BytesCoder;\n  base32: BytesCoder;\n  base64: BytesCoder;\n  base64url: BytesCoder;\n  base58: BytesCoder;\n  base58xmr: BytesCoder;\n};\n// prettier-ignore\nconst CODERS: SomeCoders = {\n  utf8, hex, base16, base32, base64, base64url, base58, base58xmr\n};\ntype CoderType = keyof SomeCoders;\nconst coderTypeError =\n  'Invalid encoding type. Available types: utf8, hex, base16, base32, base64, base64url, base58, base58xmr';\n\n/** @deprecated */\nexport const bytesToString = (type: CoderType, bytes: Uint8Array): string => {\n  if (typeof type !== 'string' || !CODERS.hasOwnProperty(type)) throw new TypeError(coderTypeError);\n  if (!isBytes(bytes)) throw new TypeError('bytesToString() expects Uint8Array');\n  return CODERS[type].encode(bytes);\n};\n\n/** @deprecated */\nexport const str: (type: CoderType, bytes: Uint8Array) => string = bytesToString; // as in python, but for bytes only\n\n/** @deprecated */\nexport const stringToBytes = (type: CoderType, str: string): Uint8Array => {\n  if (!CODERS.hasOwnProperty(type)) throw new TypeError(coderTypeError);\n  if (typeof str !== 'string') throw new TypeError('stringToBytes() expects string');\n  return CODERS[type].decode(str);\n};\n/** @deprecated */\nexport const bytes: (type: CoderType, str: string) => Uint8Array = stringToBytes;\n"], "mappings": "AAAA;AAYA,SAASA,OAAOA,CAACC,CAAU;EACzB,OAAOA,CAAC,YAAYC,UAAU,IAAKC,WAAW,CAACC,MAAM,CAACH,CAAC,CAAC,IAAIA,CAAC,CAACI,WAAW,CAACC,IAAI,KAAK,YAAa;AAClG;AACA;AACA,SAASC,MAAMA,CAACC,CAAyB,EAAE,GAAGC,OAAiB;EAC7D,IAAI,CAACT,OAAO,CAACQ,CAAC,CAAC,EAAE,MAAM,IAAIE,KAAK,CAAC,qBAAqB,CAAC;EACvD,IAAID,OAAO,CAACE,MAAM,GAAG,CAAC,IAAI,CAACF,OAAO,CAACG,QAAQ,CAACJ,CAAC,CAACG,MAAM,CAAC,EACnD,MAAM,IAAID,KAAK,CAAC,gCAAgC,GAAGD,OAAO,GAAG,eAAe,GAAGD,CAAC,CAACG,MAAM,CAAC;AAC5F;AAEA,SAASE,SAASA,CAACC,QAAiB,EAAEC,GAAU;EAC9C,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,GAAG,CAAC,EAAE,OAAO,KAAK;EACrC,IAAIA,GAAG,CAACJ,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;EACjC,IAAIG,QAAQ,EAAE;IACZ,OAAOC,GAAG,CAACG,KAAK,CAAEC,IAAI,IAAK,OAAOA,IAAI,KAAK,QAAQ,CAAC;EACtD,CAAC,MAAM;IACL,OAAOJ,GAAG,CAACG,KAAK,CAAEC,IAAI,IAAKC,MAAM,CAACC,aAAa,CAACF,IAAI,CAAC,CAAC;EACxD;AACF;AAEA;AAEA,SAASG,GAAGA,CAACC,KAAe;EAC1B,IAAI,OAAOA,KAAK,KAAK,UAAU,EAAE,MAAM,IAAIb,KAAK,CAAC,mBAAmB,CAAC;EACrE,OAAO,IAAI;AACb;AAEA,SAASc,IAAIA,CAACC,KAAa,EAAEF,KAAc;EACzC,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE,MAAM,IAAIb,KAAK,CAAC,GAAGe,KAAK,mBAAmB,CAAC;EAC3E,OAAO,IAAI;AACb;AAEA,SAASC,OAAOA,CAACC,CAAS;EACxB,IAAI,CAACP,MAAM,CAACC,aAAa,CAACM,CAAC,CAAC,EAAE,MAAM,IAAIjB,KAAK,CAAC,oBAAoBiB,CAAC,EAAE,CAAC;AACxE;AAEA,SAASC,IAAIA,CAACL,KAAY;EACxB,IAAI,CAACP,KAAK,CAACC,OAAO,CAACM,KAAK,CAAC,EAAE,MAAM,IAAIb,KAAK,CAAC,gBAAgB,CAAC;AAC9D;AACA,SAASmB,OAAOA,CAACJ,KAAa,EAAEF,KAAe;EAC7C,IAAI,CAACV,SAAS,CAAC,IAAI,EAAEU,KAAK,CAAC,EAAE,MAAM,IAAIb,KAAK,CAAC,GAAGe,KAAK,6BAA6B,CAAC;AACrF;AACA,SAASK,OAAOA,CAACL,KAAa,EAAEF,KAAe;EAC7C,IAAI,CAACV,SAAS,CAAC,KAAK,EAAEU,KAAK,CAAC,EAAE,MAAM,IAAIb,KAAK,CAAC,GAAGe,KAAK,6BAA6B,CAAC;AACtF;AAkBA;;;AAGA,SAASM,KAAKA,CAA+B,GAAGC,IAAO;EACrD,MAAMC,EAAE,GAAIhC,CAAM,IAAKA,CAAC;EACxB;EACA,MAAMiC,IAAI,GAAGA,CAACjC,CAAM,EAAEO,CAAM,KAAM2B,CAAM,IAAKlC,CAAC,CAACO,CAAC,CAAC2B,CAAC,CAAC,CAAC;EACpD;EACA,MAAMC,MAAM,GAAGJ,IAAI,CAACK,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAACF,MAAM,CAAC,CAACG,WAAW,CAACL,IAAI,EAAED,EAAE,CAAC;EAC9D;EACA,MAAMO,MAAM,GAAGR,IAAI,CAACK,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAACE,MAAM,CAAC,CAACC,MAAM,CAACP,IAAI,EAAED,EAAE,CAAC;EACzD,OAAO;IAAEG,MAAM;IAAEI;EAAM,CAAE;AAC3B;AAEA;;;;;AAKA,SAASE,QAAQA,CAACC,OAA0B;EAC1C;EACA,MAAMC,QAAQ,GAAG,OAAOD,OAAO,KAAK,QAAQ,GAAGA,OAAO,CAACE,KAAK,CAAC,EAAE,CAAC,GAAGF,OAAO;EAC1E,MAAMG,GAAG,GAAGF,QAAQ,CAACjC,MAAM;EAC3BkB,OAAO,CAAC,UAAU,EAAEe,QAAQ,CAAC;EAE7B;EACA,MAAMG,OAAO,GAAG,IAAIC,GAAG,CAACJ,QAAQ,CAACP,GAAG,CAAC,CAACY,CAAC,EAAEC,CAAC,KAAK,CAACD,CAAC,EAAEC,CAAC,CAAC,CAAC,CAAC;EACvD,OAAO;IACLd,MAAM,EAAGe,MAAgB,IAAI;MAC3BvB,IAAI,CAACuB,MAAM,CAAC;MACZ,OAAOA,MAAM,CAACd,GAAG,CAAEa,CAAC,IAAI;QACtB,IAAI,CAAC9B,MAAM,CAACC,aAAa,CAAC6B,CAAC,CAAC,IAAIA,CAAC,GAAG,CAAC,IAAIA,CAAC,IAAIJ,GAAG,EAC/C,MAAM,IAAIpC,KAAK,CACb,kDAAkDwC,CAAC,eAAeP,OAAO,EAAE,CAC5E;QACH,OAAOC,QAAQ,CAACM,CAAC,CAAE;MACrB,CAAC,CAAC;IACJ,CAAC;IACDV,MAAM,EAAGjB,KAAe,IAAc;MACpCK,IAAI,CAACL,KAAK,CAAC;MACX,OAAOA,KAAK,CAACc,GAAG,CAAEe,MAAM,IAAI;QAC1B5B,IAAI,CAAC,iBAAiB,EAAE4B,MAAM,CAAC;QAC/B,MAAMF,CAAC,GAAGH,OAAO,CAACM,GAAG,CAACD,MAAM,CAAC;QAC7B,IAAIF,CAAC,KAAKI,SAAS,EAAE,MAAM,IAAI5C,KAAK,CAAC,oBAAoB0C,MAAM,eAAeT,OAAO,EAAE,CAAC;QACxF,OAAOO,CAAC;MACV,CAAC,CAAC;IACJ;GACD;AACH;AAEA;;;AAGA,SAASK,IAAIA,CAACC,SAAS,GAAG,EAAE;EAC1BhC,IAAI,CAAC,MAAM,EAAEgC,SAAS,CAAC;EACvB,OAAO;IACLpB,MAAM,EAAGqB,IAAI,IAAI;MACf5B,OAAO,CAAC,aAAa,EAAE4B,IAAI,CAAC;MAC5B,OAAOA,IAAI,CAACF,IAAI,CAACC,SAAS,CAAC;IAC7B,CAAC;IACDhB,MAAM,EAAGkB,EAAE,IAAI;MACblC,IAAI,CAAC,aAAa,EAAEkC,EAAE,CAAC;MACvB,OAAOA,EAAE,CAACb,KAAK,CAACW,SAAS,CAAC;IAC5B;GACD;AACH;AAEA;;;;AAIA,SAASG,OAAOA,CAACC,IAAY,EAAEC,GAAG,GAAG,GAAG;EACtCnC,OAAO,CAACkC,IAAI,CAAC;EACbpC,IAAI,CAAC,SAAS,EAAEqC,GAAG,CAAC;EACpB,OAAO;IACLzB,MAAMA,CAAC0B,IAAc;MACnBjC,OAAO,CAAC,gBAAgB,EAAEiC,IAAI,CAAC;MAC/B,OAAQA,IAAI,CAACnD,MAAM,GAAGiD,IAAI,GAAI,CAAC,EAAEE,IAAI,CAACC,IAAI,CAACF,GAAG,CAAC;MAC/C,OAAOC,IAAI;IACb,CAAC;IACDtB,MAAMA,CAACjB,KAAe;MACpBM,OAAO,CAAC,gBAAgB,EAAEN,KAAK,CAAC;MAChC,IAAIyC,GAAG,GAAGzC,KAAK,CAACZ,MAAM;MACtB,IAAKqD,GAAG,GAAGJ,IAAI,GAAI,CAAC,EAClB,MAAM,IAAIlD,KAAK,CAAC,4DAA4D,CAAC;MAC/E,OAAOsD,GAAG,GAAG,CAAC,IAAIzC,KAAK,CAACyC,GAAG,GAAG,CAAC,CAAC,KAAKH,GAAG,EAAEG,GAAG,EAAE,EAAE;QAC/C,MAAMC,IAAI,GAAGD,GAAG,GAAG,CAAC;QACpB,MAAME,IAAI,GAAGD,IAAI,GAAGL,IAAI;QACxB,IAAIM,IAAI,GAAG,CAAC,KAAK,CAAC,EAAE,MAAM,IAAIxD,KAAK,CAAC,+CAA+C,CAAC;MACtF;MACA,OAAOa,KAAK,CAAC4C,KAAK,CAAC,CAAC,EAAEH,GAAG,CAAC;IAC5B;GACD;AACH;AAEA;;;AAGA,SAASI,SAASA,CAAIC,EAAiB;EACrC/C,GAAG,CAAC+C,EAAE,CAAC;EACP,OAAO;IAAEjC,MAAM,EAAGqB,IAAO,IAAKA,IAAI;IAAEjB,MAAM,EAAGkB,EAAK,IAAKW,EAAE,CAACX,EAAE;EAAC,CAAE;AACjE;AAEA;;;AAGA,SAASY,YAAYA,CAACR,IAAc,EAAEL,IAAY,EAAEC,EAAU;EAC5D;EACA,IAAID,IAAI,GAAG,CAAC,EAAE,MAAM,IAAI/C,KAAK,CAAC,8BAA8B+C,IAAI,8BAA8B,CAAC;EAC/F,IAAIC,EAAE,GAAG,CAAC,EAAE,MAAM,IAAIhD,KAAK,CAAC,4BAA4BgD,EAAE,8BAA8B,CAAC;EACzF9B,IAAI,CAACkC,IAAI,CAAC;EACV,IAAI,CAACA,IAAI,CAACnD,MAAM,EAAE,OAAO,EAAE;EAC3B,IAAI4D,GAAG,GAAG,CAAC;EACX,MAAMC,GAAG,GAAG,EAAE;EACd,MAAMrB,MAAM,GAAGnC,KAAK,CAACyC,IAAI,CAACK,IAAI,EAAGW,CAAC,IAAI;IACpC/C,OAAO,CAAC+C,CAAC,CAAC;IACV,IAAIA,CAAC,GAAG,CAAC,IAAIA,CAAC,IAAIhB,IAAI,EAAE,MAAM,IAAI/C,KAAK,CAAC,oBAAoB+D,CAAC,EAAE,CAAC;IAChE,OAAOA,CAAC;EACV,CAAC,CAAC;EACF,MAAMC,IAAI,GAAGvB,MAAM,CAACxC,MAAM;EAC1B,OAAO,IAAI,EAAE;IACX,IAAIgE,KAAK,GAAG,CAAC;IACb,IAAIC,IAAI,GAAG,IAAI;IACf,KAAK,IAAI1B,CAAC,GAAGqB,GAAG,EAAErB,CAAC,GAAGwB,IAAI,EAAExB,CAAC,EAAE,EAAE;MAC/B,MAAM2B,KAAK,GAAG1B,MAAM,CAACD,CAAC,CAAE;MACxB,MAAM4B,SAAS,GAAGrB,IAAI,GAAGkB,KAAK;MAC9B,MAAMI,SAAS,GAAGD,SAAS,GAAGD,KAAK;MACnC,IACE,CAACzD,MAAM,CAACC,aAAa,CAAC0D,SAAS,CAAC,IAChCD,SAAS,GAAGrB,IAAI,KAAKkB,KAAK,IAC1BI,SAAS,GAAGF,KAAK,KAAKC,SAAS,EAC/B;QACA,MAAM,IAAIpE,KAAK,CAAC,8BAA8B,CAAC;MACjD;MACA,MAAMsE,GAAG,GAAGD,SAAS,GAAGrB,EAAE;MAC1BiB,KAAK,GAAGI,SAAS,GAAGrB,EAAE;MACtB,MAAMuB,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,GAAG,CAAC;MAC/B7B,MAAM,CAACD,CAAC,CAAC,GAAG+B,OAAO;MACnB,IAAI,CAAC7D,MAAM,CAACC,aAAa,CAAC4D,OAAO,CAAC,IAAIA,OAAO,GAAGvB,EAAE,GAAGiB,KAAK,KAAKI,SAAS,EACtE,MAAM,IAAIrE,KAAK,CAAC,8BAA8B,CAAC;MACjD,IAAI,CAACkE,IAAI,EAAE,SAAS,KACf,IAAI,CAACK,OAAO,EAAEV,GAAG,GAAGrB,CAAC,CAAC,KACtB0B,IAAI,GAAG,KAAK;IACnB;IACAJ,GAAG,CAACT,IAAI,CAACY,KAAK,CAAC;IACf,IAAIC,IAAI,EAAE;EACZ;EACA,KAAK,IAAI1B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,IAAI,CAACnD,MAAM,GAAG,CAAC,IAAImD,IAAI,CAACZ,CAAC,CAAC,KAAK,CAAC,EAAEA,CAAC,EAAE,EAAEsB,GAAG,CAACT,IAAI,CAAC,CAAC,CAAC;EACtE,OAAOS,GAAG,CAACY,OAAO,EAAE;AACtB;AAEA,MAAMC,GAAG,GAAGA,CAACpF,CAAS,EAAEO,CAAS,KAAcA,CAAC,KAAK,CAAC,GAAGP,CAAC,GAAGoF,GAAG,CAAC7E,CAAC,EAAEP,CAAC,GAAGO,CAAC,CAAE;AAC3E,MAAM8E,WAAW,GAAG,0BAA2BA,CAAC7B,IAAY,EAAEC,EAAU,KACtED,IAAI,IAAIC,EAAE,GAAG2B,GAAG,CAAC5B,IAAI,EAAEC,EAAE,CAAC,CAAC;AAC7B,MAAM6B,MAAM,GAAa,eAAgB,CAAC,MAAK;EAC7C,IAAIf,GAAG,GAAG,EAAE;EACZ,KAAK,IAAItB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAEsB,GAAG,CAACT,IAAI,CAAC,CAAC,IAAIb,CAAC,CAAC;EAC7C,OAAOsB,GAAG;AACZ,CAAC,EAAC,CAAE;AACJ;;;AAGA,SAASgB,aAAaA,CAAC1B,IAAc,EAAEL,IAAY,EAAEC,EAAU,EAAEC,OAAgB;EAC/E/B,IAAI,CAACkC,IAAI,CAAC;EACV,IAAIL,IAAI,IAAI,CAAC,IAAIA,IAAI,GAAG,EAAE,EAAE,MAAM,IAAI/C,KAAK,CAAC,6BAA6B+C,IAAI,EAAE,CAAC;EAChF,IAAIC,EAAE,IAAI,CAAC,IAAIA,EAAE,GAAG,EAAE,EAAE,MAAM,IAAIhD,KAAK,CAAC,2BAA2BgD,EAAE,EAAE,CAAC;EACxE,IAAI4B,WAAW,CAAC7B,IAAI,EAAEC,EAAE,CAAC,GAAG,EAAE,EAAE;IAC9B,MAAM,IAAIhD,KAAK,CACb,sCAAsC+C,IAAI,OAAOC,EAAE,cAAc4B,WAAW,CAAC7B,IAAI,EAAEC,EAAE,CAAC,EAAE,CACzF;EACH;EACA,IAAIiB,KAAK,GAAG,CAAC;EACb,IAAIJ,GAAG,GAAG,CAAC,CAAC,CAAC;EACb,MAAMkB,GAAG,GAAGF,MAAM,CAAC9B,IAAI,CAAE;EACzB,MAAMiC,IAAI,GAAGH,MAAM,CAAC7B,EAAE,CAAE,GAAG,CAAC;EAC5B,MAAMc,GAAG,GAAa,EAAE;EACxB,KAAK,MAAM7C,CAAC,IAAImC,IAAI,EAAE;IACpBpC,OAAO,CAACC,CAAC,CAAC;IACV,IAAIA,CAAC,IAAI8D,GAAG,EAAE,MAAM,IAAI/E,KAAK,CAAC,oCAAoCiB,CAAC,SAAS8B,IAAI,EAAE,CAAC;IACnFkB,KAAK,GAAIA,KAAK,IAAIlB,IAAI,GAAI9B,CAAC;IAC3B,IAAI4C,GAAG,GAAGd,IAAI,GAAG,EAAE,EAAE,MAAM,IAAI/C,KAAK,CAAC,qCAAqC6D,GAAG,SAASd,IAAI,EAAE,CAAC;IAC7Fc,GAAG,IAAId,IAAI;IACX,OAAOc,GAAG,IAAIb,EAAE,EAAEa,GAAG,IAAIb,EAAE,EAAEc,GAAG,CAACT,IAAI,CAAC,CAAEY,KAAK,IAAKJ,GAAG,GAAGb,EAAG,GAAIgC,IAAI,MAAM,CAAC,CAAC;IAC3E,MAAMC,GAAG,GAAGJ,MAAM,CAAChB,GAAG,CAAC;IACvB,IAAIoB,GAAG,KAAKrC,SAAS,EAAE,MAAM,IAAI5C,KAAK,CAAC,eAAe,CAAC;IACvDiE,KAAK,IAAIgB,GAAG,GAAG,CAAC,CAAC,CAAC;EACpB;EACAhB,KAAK,GAAIA,KAAK,IAAKjB,EAAE,GAAGa,GAAI,GAAImB,IAAI;EACpC,IAAI,CAAC/B,OAAO,IAAIY,GAAG,IAAId,IAAI,EAAE,MAAM,IAAI/C,KAAK,CAAC,gBAAgB,CAAC;EAC9D,IAAI,CAACiD,OAAO,IAAIgB,KAAK,GAAG,CAAC,EAAE,MAAM,IAAIjE,KAAK,CAAC,qBAAqBiE,KAAK,EAAE,CAAC;EACxE,IAAIhB,OAAO,IAAIY,GAAG,GAAG,CAAC,EAAEC,GAAG,CAACT,IAAI,CAACY,KAAK,KAAK,CAAC,CAAC;EAC7C,OAAOH,GAAG;AACZ;AAEA;;;AAGA,SAASoB,KAAKA,CAACC,GAAW;EACxBnE,OAAO,CAACmE,GAAG,CAAC;EACZ,MAAMC,IAAI,GAAG,CAAC,IAAI,CAAC;EACnB,OAAO;IACL1D,MAAM,EAAG2D,KAAiB,IAAI;MAC5B,IAAI,CAAC/F,OAAO,CAAC+F,KAAK,CAAC,EAAE,MAAM,IAAIrF,KAAK,CAAC,yCAAyC,CAAC;MAC/E,OAAO4D,YAAY,CAACtD,KAAK,CAACyC,IAAI,CAACsC,KAAK,CAAC,EAAED,IAAI,EAAED,GAAG,CAAC;IACnD,CAAC;IACDrD,MAAM,EAAGW,MAAgB,IAAI;MAC3BrB,OAAO,CAAC,cAAc,EAAEqB,MAAM,CAAC;MAC/B,OAAOjD,UAAU,CAACuD,IAAI,CAACa,YAAY,CAACnB,MAAM,EAAE0C,GAAG,EAAEC,IAAI,CAAC,CAAC;IACzD;GACD;AACH;AAEA;;;;;AAKA,SAASE,MAAMA,CAACpC,IAAY,EAAEqC,UAAU,GAAG,KAAK;EAC9CvE,OAAO,CAACkC,IAAI,CAAC;EACb,IAAIA,IAAI,IAAI,CAAC,IAAIA,IAAI,GAAG,EAAE,EAAE,MAAM,IAAIlD,KAAK,CAAC,mCAAmC,CAAC;EAChF,IAAI4E,WAAW,CAAC,CAAC,EAAE1B,IAAI,CAAC,GAAG,EAAE,IAAI0B,WAAW,CAAC1B,IAAI,EAAE,CAAC,CAAC,GAAG,EAAE,EACxD,MAAM,IAAIlD,KAAK,CAAC,wBAAwB,CAAC;EAC3C,OAAO;IACL0B,MAAM,EAAG2D,KAAiB,IAAI;MAC5B,IAAI,CAAC/F,OAAO,CAAC+F,KAAK,CAAC,EAAE,MAAM,IAAIrF,KAAK,CAAC,0CAA0C,CAAC;MAChF,OAAO8E,aAAa,CAACxE,KAAK,CAACyC,IAAI,CAACsC,KAAK,CAAC,EAAE,CAAC,EAAEnC,IAAI,EAAE,CAACqC,UAAU,CAAC;IAC/D,CAAC;IACDzD,MAAM,EAAGW,MAAgB,IAAI;MAC3BrB,OAAO,CAAC,eAAe,EAAEqB,MAAM,CAAC;MAChC,OAAOjD,UAAU,CAACuD,IAAI,CAAC+B,aAAa,CAACrC,MAAM,EAAES,IAAI,EAAE,CAAC,EAAEqC,UAAU,CAAC,CAAC;IACpE;GACD;AACH;AAGA,SAASC,aAAaA,CAAkC7B,EAAK;EAC3D/C,GAAG,CAAC+C,EAAE,CAAC;EACP,OAAO,UAAU,GAAGrC,IAAsB;IACxC,IAAI;MACF,OAAOqC,EAAE,CAAC8B,KAAK,CAAC,IAAI,EAAEnE,IAAI,CAAC;IAC7B,CAAC,CAAC,OAAOoE,CAAC,EAAE,CAAC;EACf,CAAC;AACH;AAEA,SAASC,QAAQA,CACfvD,GAAW,EACXuB,EAAoC;EAEpC3C,OAAO,CAACoB,GAAG,CAAC;EACZxB,GAAG,CAAC+C,EAAE,CAAC;EACP,OAAO;IACLjC,MAAMA,CAAC0B,IAAgB;MACrB,IAAI,CAAC9D,OAAO,CAAC8D,IAAI,CAAC,EAAE,MAAM,IAAIpD,KAAK,CAAC,6CAA6C,CAAC;MAClF,MAAM4F,GAAG,GAAGjC,EAAE,CAACP,IAAI,CAAC,CAACK,KAAK,CAAC,CAAC,EAAErB,GAAG,CAAC;MAClC,MAAM0B,GAAG,GAAG,IAAItE,UAAU,CAAC4D,IAAI,CAACnD,MAAM,GAAGmC,GAAG,CAAC;MAC7C0B,GAAG,CAAC+B,GAAG,CAACzC,IAAI,CAAC;MACbU,GAAG,CAAC+B,GAAG,CAACD,GAAG,EAAExC,IAAI,CAACnD,MAAM,CAAC;MACzB,OAAO6D,GAAG;IACZ,CAAC;IACDhC,MAAMA,CAACsB,IAAgB;MACrB,IAAI,CAAC9D,OAAO,CAAC8D,IAAI,CAAC,EAAE,MAAM,IAAIpD,KAAK,CAAC,6CAA6C,CAAC;MAClF,MAAM8F,OAAO,GAAG1C,IAAI,CAACK,KAAK,CAAC,CAAC,EAAE,CAACrB,GAAG,CAAC;MACnC,MAAM2D,WAAW,GAAG3C,IAAI,CAACK,KAAK,CAAC,CAACrB,GAAG,CAAC;MACpC,MAAM4D,WAAW,GAAGrC,EAAE,CAACmC,OAAO,CAAC,CAACrC,KAAK,CAAC,CAAC,EAAErB,GAAG,CAAC;MAC7C,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,GAAG,EAAEI,CAAC,EAAE,EAC1B,IAAIwD,WAAW,CAACxD,CAAC,CAAC,KAAKuD,WAAW,CAACvD,CAAC,CAAC,EAAE,MAAM,IAAIxC,KAAK,CAAC,kBAAkB,CAAC;MAC5E,OAAO8F,OAAO;IAChB;GACD;AACH;AAEA;AACA,OAAO,MAAMG,KAAK,GAAmP;EACnQjE,QAAQ;EAAEX,KAAK;EAAEsE,QAAQ;EAAE/B,YAAY;EAAEkB,aAAa;EAAEI,KAAK;EAAEI,MAAM;EAAEzC,IAAI;EAAEI;CAC9E;AAED;AACA;AAEA;;;;;;;;AAQA,OAAO,MAAMiD,MAAM,GAAe7E,KAAK,CAACiE,MAAM,CAAC,CAAC,CAAC,EAAEtD,QAAQ,CAAC,kBAAkB,CAAC,EAAEa,IAAI,CAAC,EAAE,CAAC,CAAC;AAE1F;;;;;;;;;;;;AAYA,OAAO,MAAMsD,MAAM,GAAe9E,KAAK,CACrCiE,MAAM,CAAC,CAAC,CAAC,EACTtD,QAAQ,CAAC,kCAAkC,CAAC,EAC5CiB,OAAO,CAAC,CAAC,CAAC,EACVJ,IAAI,CAAC,EAAE,CAAC,CACT;AAED;;;;;;;;;;;;AAYA,OAAO,MAAMuD,WAAW,GAAe/E,KAAK,CAC1CiE,MAAM,CAAC,CAAC,CAAC,EACTtD,QAAQ,CAAC,kCAAkC,CAAC,EAC5Ca,IAAI,CAAC,EAAE,CAAC,CACT;AACD;;;;;;;;;;;AAWA,OAAO,MAAMwD,SAAS,GAAehF,KAAK,CACxCiE,MAAM,CAAC,CAAC,CAAC,EACTtD,QAAQ,CAAC,kCAAkC,CAAC,EAC5CiB,OAAO,CAAC,CAAC,CAAC,EACVJ,IAAI,CAAC,EAAE,CAAC,CACT;AAED;;;;;;;;;;;AAWA,OAAO,MAAMyD,cAAc,GAAejF,KAAK,CAC7CiE,MAAM,CAAC,CAAC,CAAC,EACTtD,QAAQ,CAAC,kCAAkC,CAAC,EAC5Ca,IAAI,CAAC,EAAE,CAAC,CACT;AACD;;;;;;;;;;;AAWA,OAAO,MAAM0D,eAAe,GAAelF,KAAK,CAC9CiE,MAAM,CAAC,CAAC,CAAC,EACTtD,QAAQ,CAAC,kCAAkC,CAAC,EAC5Ca,IAAI,CAAC,EAAE,CAAC,EACRa,SAAS,CAAE8C,CAAS,IAAKA,CAAC,CAACC,WAAW,EAAE,CAACC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CACnF;AAED;AACA;AACA,MAAMC,gBAAgB,GAAY,eAAgB,CAAC,MACjD,OAAQnH,UAAkB,CAACuD,IAAI,CAAC,EAAE,CAAC,CAAC6D,QAAQ,KAAK,UAAU,IAC3D,OAAQpH,UAAkB,CAACqH,UAAU,KAAK,UAAU,EAAC,CAAE;AAEzD,MAAMC,mBAAmB,GAAGA,CAACN,CAAS,EAAEO,KAAc,KAAI;EACxDjG,IAAI,CAAC,QAAQ,EAAE0F,CAAC,CAAC;EACjB,MAAMQ,EAAE,GAAGD,KAAK,GAAG,mBAAmB,GAAG,mBAAmB;EAC5D,MAAM/E,QAAQ,GAAG+E,KAAK,GAAG,WAAW,GAAG,QAAQ;EAC/C,IAAIP,CAAC,CAACvG,MAAM,GAAG,CAAC,IAAI,CAAC+G,EAAE,CAACC,IAAI,CAACT,CAAC,CAAC,EAAE,MAAM,IAAIxG,KAAK,CAAC,gBAAgB,CAAC;EAClE,OAAQR,UAAkB,CAACqH,UAAU,CAACL,CAAC,EAAE;IAAExE,QAAQ;IAAEkF,iBAAiB,EAAE;EAAQ,CAAE,CAAC;AACrF,CAAC;AAED;;;;;;;;;;;;;AAaA;AACA,OAAO,MAAMC,MAAM,GAAeR,gBAAgB,GAAG;EACnDjF,MAAMA,CAAC5B,CAAC;IAAID,MAAM,CAACC,CAAC,CAAC;IAAE,OAAQA,CAAS,CAAC8G,QAAQ,EAAE;EAAE,CAAC;EACtD9E,MAAMA,CAAC0E,CAAC;IAAI,OAAOM,mBAAmB,CAACN,CAAC,EAAE,KAAK,CAAC;EAAE;CACnD,GAAGnF,KAAK,CACPiE,MAAM,CAAC,CAAC,CAAC,EACTtD,QAAQ,CAAC,kEAAkE,CAAC,EAC5EiB,OAAO,CAAC,CAAC,CAAC,EACVJ,IAAI,CAAC,EAAE,CAAC,CACT;AACD;;;;;;;;;;;AAWA,OAAO,MAAMuE,WAAW,GAAe/F,KAAK,CAC1CiE,MAAM,CAAC,CAAC,CAAC,EACTtD,QAAQ,CAAC,kEAAkE,CAAC,EAC5Ea,IAAI,CAAC,EAAE,CAAC,CACT;AAED;;;;;;;;;;;;AAYA;AACA,OAAO,MAAMwE,SAAS,GAAeV,gBAAgB,GAAG;EACtDjF,MAAMA,CAAC5B,CAAC;IAAID,MAAM,CAACC,CAAC,CAAC;IAAE,OAAQA,CAAS,CAAC8G,QAAQ,CAAC;MAAE5E,QAAQ,EAAE;IAAW,CAAE,CAAC;EAAE,CAAC;EAC/EF,MAAMA,CAAC0E,CAAC;IAAI,OAAOM,mBAAmB,CAACN,CAAC,EAAE,IAAI,CAAC;EAAE;CAClD,GAAGnF,KAAK,CACPiE,MAAM,CAAC,CAAC,CAAC,EACTtD,QAAQ,CAAC,kEAAkE,CAAC,EAC5EiB,OAAO,CAAC,CAAC,CAAC,EACVJ,IAAI,CAAC,EAAE,CAAC,CACT;AAED;;;;;;;;;;;AAWA,OAAO,MAAMyE,cAAc,GAAejG,KAAK,CAC7CiE,MAAM,CAAC,CAAC,CAAC,EACTtD,QAAQ,CAAC,kEAAkE,CAAC,EAC5Ea,IAAI,CAAC,EAAE,CAAC,CACT;AAED;AACA;AACA,MAAM0E,SAAS,GAAG,0BAA4BC,GAAW,IACvDnG,KAAK,CAAC6D,KAAK,CAAC,EAAE,CAAC,EAAElD,QAAQ,CAACwF,GAAG,CAAC,EAAE3E,IAAI,CAAC,EAAE,CAAC,CAAC;AAE3C;;;;;;;;;AASA,OAAO,MAAM4E,MAAM,GAAeF,SAAS,CACzC,4DAA4D,CAC7D;AACD;;;AAGA,OAAO,MAAMG,YAAY,GAAeH,SAAS,CAC/C,4DAA4D,CAC7D;AACD;;;AAGA,OAAO,MAAMI,SAAS,GAAeJ,SAAS,CAC5C,4DAA4D,CAC7D;AAED;AACA,MAAMK,aAAa,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;AAEnD;;;;;AAKA,OAAO,MAAMC,SAAS,GAAe;EACnCnG,MAAMA,CAAC0B,IAAgB;IACrB,IAAIU,GAAG,GAAG,EAAE;IACZ,KAAK,IAAItB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,IAAI,CAACnD,MAAM,EAAEuC,CAAC,IAAI,CAAC,EAAE;MACvC,MAAMsF,KAAK,GAAG1E,IAAI,CAAC2E,QAAQ,CAACvF,CAAC,EAAEA,CAAC,GAAG,CAAC,CAAC;MACrCsB,GAAG,IAAI2D,MAAM,CAAC/F,MAAM,CAACoG,KAAK,CAAC,CAACE,QAAQ,CAACJ,aAAa,CAACE,KAAK,CAAC7H,MAAM,CAAE,EAAE,GAAG,CAAC;IACzE;IACA,OAAO6D,GAAG;EACZ,CAAC;EACDhC,MAAMA,CAACmG,GAAW;IAChB,IAAInE,GAAG,GAAa,EAAE;IACtB,KAAK,IAAItB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyF,GAAG,CAAChI,MAAM,EAAEuC,CAAC,IAAI,EAAE,EAAE;MACvC,MAAMiB,KAAK,GAAGwE,GAAG,CAACxE,KAAK,CAACjB,CAAC,EAAEA,CAAC,GAAG,EAAE,CAAC;MAClC,MAAM0F,QAAQ,GAAGN,aAAa,CAACO,OAAO,CAAC1E,KAAK,CAACxD,MAAM,CAAC;MACpD,MAAM6H,KAAK,GAAGL,MAAM,CAAC3F,MAAM,CAAC2B,KAAK,CAAC;MAClC,KAAK,IAAI2E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,KAAK,CAAC7H,MAAM,GAAGiI,QAAQ,EAAEE,CAAC,EAAE,EAAE;QAChD,IAAIN,KAAK,CAACM,CAAC,CAAC,KAAK,CAAC,EAAE,MAAM,IAAIpI,KAAK,CAAC,0BAA0B,CAAC;MACjE;MACA8D,GAAG,GAAGA,GAAG,CAACuE,MAAM,CAAC/H,KAAK,CAACyC,IAAI,CAAC+E,KAAK,CAACrE,KAAK,CAACqE,KAAK,CAAC7H,MAAM,GAAGiI,QAAQ,CAAC,CAAC,CAAC;IACpE;IACA,OAAO1I,UAAU,CAACuD,IAAI,CAACe,GAAG,CAAC;EAC7B;CACD;AAED;;;;AAIA,OAAO,MAAMwE,iBAAiB,GAAIC,MAAwC,IACxElH,KAAK,CACHsE,QAAQ,CAAC,CAAC,EAAGvC,IAAI,IAAKmF,MAAM,CAACA,MAAM,CAACnF,IAAI,CAAC,CAAC,CAAC,EAC3CqE,MAAM,CACP;AAEH;;;;AAIA,OAAO,MAAMe,WAAW,GACtBF,iBAAiB;AAcnB,MAAMG,aAAa,GAA4BpH,KAAK,CAClDW,QAAQ,CAAC,kCAAkC,CAAC,EAC5Ca,IAAI,CAAC,EAAE,CAAC,CACT;AAED,MAAM6F,kBAAkB,GAAG,CAAC,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,CAAC;AACvF,SAASC,aAAaA,CAACC,GAAW;EAChC,MAAM9I,CAAC,GAAG8I,GAAG,IAAI,EAAE;EACnB,IAAIC,GAAG,GAAG,CAACD,GAAG,GAAG,SAAS,KAAK,CAAC;EAChC,KAAK,IAAIpG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkG,kBAAkB,CAACzI,MAAM,EAAEuC,CAAC,EAAE,EAAE;IAClD,IAAI,CAAE1C,CAAC,IAAI0C,CAAC,GAAI,CAAC,MAAM,CAAC,EAAEqG,GAAG,IAAIH,kBAAkB,CAAClG,CAAC,CAAE;EACzD;EACA,OAAOqG,GAAG;AACZ;AAEA,SAASC,YAAYA,CAACC,MAAc,EAAEC,KAAe,EAAEC,aAAa,GAAG,CAAC;EACtE,MAAM7G,GAAG,GAAG2G,MAAM,CAAC9I,MAAM;EACzB,IAAI4I,GAAG,GAAG,CAAC;EACX,KAAK,IAAIrG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,GAAG,EAAEI,CAAC,EAAE,EAAE;IAC5B,MAAMf,CAAC,GAAGsH,MAAM,CAACG,UAAU,CAAC1G,CAAC,CAAC;IAC9B,IAAIf,CAAC,GAAG,EAAE,IAAIA,CAAC,GAAG,GAAG,EAAE,MAAM,IAAIzB,KAAK,CAAC,mBAAmB+I,MAAM,GAAG,CAAC;IACpEF,GAAG,GAAGF,aAAa,CAACE,GAAG,CAAC,GAAIpH,CAAC,IAAI,CAAE;EACrC;EACAoH,GAAG,GAAGF,aAAa,CAACE,GAAG,CAAC;EACxB,KAAK,IAAIrG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,GAAG,EAAEI,CAAC,EAAE,EAAEqG,GAAG,GAAGF,aAAa,CAACE,GAAG,CAAC,GAAIE,MAAM,CAACG,UAAU,CAAC1G,CAAC,CAAC,GAAG,IAAK;EACtF,KAAK,IAAI2G,CAAC,IAAIH,KAAK,EAAEH,GAAG,GAAGF,aAAa,CAACE,GAAG,CAAC,GAAGM,CAAC;EACjD,KAAK,IAAI3G,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAEqG,GAAG,GAAGF,aAAa,CAACE,GAAG,CAAC;EACpDA,GAAG,IAAII,aAAa;EACpB,OAAOR,aAAa,CAAC/G,MAAM,CAACoD,aAAa,CAAC,CAAC+D,GAAG,GAAGhE,MAAM,CAAC,EAAE,CAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;AAC/E;AAmBA;;;AAGA,SAASuE,SAASA,CAACC,QAA8B;EAC/C,MAAMC,cAAc,GAAGD,QAAQ,KAAK,QAAQ,GAAG,CAAC,GAAG,UAAU;EAC7D,MAAME,MAAM,GAAGjE,MAAM,CAAC,CAAC,CAAC;EACxB,MAAMkE,SAAS,GAAGD,MAAM,CAACzH,MAAM;EAC/B,MAAM2H,OAAO,GAAGF,MAAM,CAAC7H,MAAM;EAC7B,MAAMgI,eAAe,GAAGlE,aAAa,CAACgE,SAAS,CAAC;EAEhD,SAAS9H,MAAMA,CACbqH,MAAc,EACdC,KAA4B,EAC5BW,KAAA,GAAwB,EAAE;IAE1B7I,IAAI,CAAC,sBAAsB,EAAEiI,MAAM,CAAC;IACpC,IAAIzJ,OAAO,CAAC0J,KAAK,CAAC,EAAEA,KAAK,GAAG1I,KAAK,CAACyC,IAAI,CAACiG,KAAK,CAAC;IAC7C5H,OAAO,CAAC,eAAe,EAAE4H,KAAK,CAAC;IAC/B,MAAMY,IAAI,GAAGb,MAAM,CAAC9I,MAAM;IAC1B,IAAI2J,IAAI,KAAK,CAAC,EAAE,MAAM,IAAIC,SAAS,CAAC,yBAAyBD,IAAI,EAAE,CAAC;IACpE,MAAME,YAAY,GAAGF,IAAI,GAAG,CAAC,GAAGZ,KAAK,CAAC/I,MAAM;IAC5C,IAAI0J,KAAK,KAAK,KAAK,IAAIG,YAAY,GAAGH,KAAK,EACzC,MAAM,IAAIE,SAAS,CAAC,UAAUC,YAAY,kBAAkBH,KAAK,EAAE,CAAC;IACtE,MAAMI,OAAO,GAAGhB,MAAM,CAACiB,WAAW,EAAE;IACpC,MAAMpE,GAAG,GAAGkD,YAAY,CAACiB,OAAO,EAAEf,KAAK,EAAEM,cAAc,CAAC;IACxD,OAAO,GAAGS,OAAO,IAAItB,aAAa,CAAC/G,MAAM,CAACsH,KAAK,CAAC,GAAGpD,GAAG,EAAsC;EAC9F;EAOA,SAAS9D,MAAMA,CAACmG,GAAW,EAAE0B,KAAA,GAAwB,EAAE;IACrD7I,IAAI,CAAC,qBAAqB,EAAEmH,GAAG,CAAC;IAChC,MAAMgC,IAAI,GAAGhC,GAAG,CAAChI,MAAM;IACvB,IAAIgK,IAAI,GAAG,CAAC,IAAKN,KAAK,KAAK,KAAK,IAAIM,IAAI,GAAGN,KAAM,EAC/C,MAAM,IAAIE,SAAS,CAAC,0BAA0BI,IAAI,KAAKhC,GAAG,mBAAmB0B,KAAK,GAAG,CAAC;IACxF;IACA,MAAMI,OAAO,GAAG9B,GAAG,CAAC+B,WAAW,EAAE;IACjC,IAAI/B,GAAG,KAAK8B,OAAO,IAAI9B,GAAG,KAAKA,GAAG,CAACxB,WAAW,EAAE,EAC9C,MAAM,IAAIzG,KAAK,CAAC,uCAAuC,CAAC;IAC1D,MAAMkK,QAAQ,GAAGH,OAAO,CAACI,WAAW,CAAC,GAAG,CAAC;IACzC,IAAID,QAAQ,KAAK,CAAC,IAAIA,QAAQ,KAAK,CAAC,CAAC,EACnC,MAAM,IAAIlK,KAAK,CAAC,yDAAyD,CAAC;IAC5E,MAAM+I,MAAM,GAAGgB,OAAO,CAACtG,KAAK,CAAC,CAAC,EAAEyG,QAAQ,CAAC;IACzC,MAAM9G,IAAI,GAAG2G,OAAO,CAACtG,KAAK,CAACyG,QAAQ,GAAG,CAAC,CAAC;IACxC,IAAI9G,IAAI,CAACnD,MAAM,GAAG,CAAC,EAAE,MAAM,IAAID,KAAK,CAAC,yCAAyC,CAAC;IAC/E,MAAMgJ,KAAK,GAAGP,aAAa,CAAC3G,MAAM,CAACsB,IAAI,CAAC,CAACK,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACrD,MAAMmC,GAAG,GAAGkD,YAAY,CAACC,MAAM,EAAEC,KAAK,EAAEM,cAAc,CAAC;IACvD,IAAI,CAAClG,IAAI,CAACgH,QAAQ,CAACxE,GAAG,CAAC,EAAE,MAAM,IAAI5F,KAAK,CAAC,uBAAuBiI,GAAG,eAAerC,GAAG,GAAG,CAAC;IACzF,OAAO;MAAEmD,MAAM;MAAEC;IAAK,CAAE;EAC1B;EAEA,MAAMqB,YAAY,GAAG7E,aAAa,CAAC1D,MAAM,CAAC;EAE1C,SAASwI,aAAaA,CAACrC,GAAW;IAChC,MAAM;MAAEc,MAAM;MAAEC;IAAK,CAAE,GAAGlH,MAAM,CAACmG,GAAG,EAAE,KAAK,CAAC;IAC5C,OAAO;MAAEc,MAAM;MAAEC,KAAK;MAAE3D,KAAK,EAAEmE,SAAS,CAACR,KAAK;IAAC,CAAE;EACnD;EAEA,SAASuB,eAAeA,CAACxB,MAAc,EAAE1D,KAAiB;IACxD,OAAO3D,MAAM,CAACqH,MAAM,EAAEU,OAAO,CAACpE,KAAK,CAAC,CAAC;EACvC;EAEA,OAAO;IACL3D,MAAM;IACNI,MAAM;IACNyI,eAAe;IACfD,aAAa;IACbD,YAAY;IACZb,SAAS;IACTE,eAAe;IACfD;GACD;AACH;AAEA;;;;;AAKA,OAAO,MAAMe,MAAM,GAAWpB,SAAS,CAAC,QAAQ,CAAC;AAEjD;;;;;;AAMA,OAAO,MAAMqB,OAAO,GAAWrB,SAAS,CAAC,SAAS,CAAC;AAKnD;;;;;;;;AAQA,OAAO,MAAMsB,IAAI,GAAe;EAC9BhJ,MAAM,EAAG0B,IAAI,IAAK,IAAIuH,WAAW,EAAE,CAAC7I,MAAM,CAACsB,IAAI,CAAC;EAChDtB,MAAM,EAAGmG,GAAG,IAAK,IAAI2C,WAAW,EAAE,CAAClJ,MAAM,CAACuG,GAAG;CAC9C;AAED;AACA;AACA,MAAM4C,aAAa,GAAY,eAAgB,CAAC,MAC9C,OAAQrL,UAAkB,CAACuD,IAAI,CAAC,EAAE,CAAC,CAAC+H,KAAK,KAAK,UAAU,IACxD,OAAQtL,UAAkB,CAACuL,OAAO,KAAK,UAAU,EAAC,CAAE;AACtD;AACA,MAAMC,UAAU,GAAe;EAC7BtJ,MAAMA,CAAC0B,IAAI;IAAIvD,MAAM,CAACuD,IAAI,CAAC;IAAE,OAAQA,IAAY,CAAC0H,KAAK,EAAE;EAAE,CAAC;EAC5DhJ,MAAMA,CAAC0E,CAAC;IAAI1F,IAAI,CAAC,KAAK,EAAE0F,CAAC,CAAC;IAAE,OAAQhH,UAAkB,CAACuL,OAAO,CAACvE,CAAC,CAAC;EAAE;CACpE;AACD;;;;;;;;AAQA,OAAO,MAAMyE,GAAG,GAAeJ,aAAa,GACxCG,UAAU,GACV3J,KAAK,CACHiE,MAAM,CAAC,CAAC,CAAC,EACTtD,QAAQ,CAAC,kBAAkB,CAAC,EAC5Ba,IAAI,CAAC,EAAE,CAAC,EACRa,SAAS,CAAE8C,CAAS,IAAI;EACtB,IAAI,OAAOA,CAAC,KAAK,QAAQ,IAAIA,CAAC,CAACvG,MAAM,GAAG,CAAC,KAAK,CAAC,EAC7C,MAAM,IAAI4J,SAAS,CACjB,oCAAoC,OAAOrD,CAAC,gBAAgBA,CAAC,CAACvG,MAAM,EAAE,CACvE;EACH,OAAOuG,CAAC,CAACwD,WAAW,EAAE;AACxB,CAAC,CAAC,CACH;AAYL;AACA,MAAMkB,MAAM,GAAe;EACzBR,IAAI;EAAEO,GAAG;EAAE/E,MAAM;EAAEC,MAAM;EAAEgB,MAAM;EAAEE,SAAS;EAAEI,MAAM;EAAEI;CACvD;AAED,MAAMsD,cAAc,GAClB,yGAAyG;AAE3G;AACA,OAAO,MAAMC,aAAa,GAAGA,CAACC,IAAe,EAAEhG,KAAiB,KAAY;EAC1E,IAAI,OAAOgG,IAAI,KAAK,QAAQ,IAAI,CAACH,MAAM,CAACI,cAAc,CAACD,IAAI,CAAC,EAAE,MAAM,IAAIxB,SAAS,CAACsB,cAAc,CAAC;EACjG,IAAI,CAAC7L,OAAO,CAAC+F,KAAK,CAAC,EAAE,MAAM,IAAIwE,SAAS,CAAC,oCAAoC,CAAC;EAC9E,OAAOqB,MAAM,CAACG,IAAI,CAAC,CAAC3J,MAAM,CAAC2D,KAAK,CAAC;AACnC,CAAC;AAED;AACA,OAAO,MAAM4C,GAAG,GAAmDmD,aAAa,CAAC,CAAC;AAElF;AACA,OAAO,MAAMG,aAAa,GAAGA,CAACF,IAAe,EAAEpD,GAAW,KAAgB;EACxE,IAAI,CAACiD,MAAM,CAACI,cAAc,CAACD,IAAI,CAAC,EAAE,MAAM,IAAIxB,SAAS,CAACsB,cAAc,CAAC;EACrE,IAAI,OAAOlD,GAAG,KAAK,QAAQ,EAAE,MAAM,IAAI4B,SAAS,CAAC,gCAAgC,CAAC;EAClF,OAAOqB,MAAM,CAACG,IAAI,CAAC,CAACvJ,MAAM,CAACmG,GAAG,CAAC;AACjC,CAAC;AACD;AACA,OAAO,MAAM5C,KAAK,GAAiDkG,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}