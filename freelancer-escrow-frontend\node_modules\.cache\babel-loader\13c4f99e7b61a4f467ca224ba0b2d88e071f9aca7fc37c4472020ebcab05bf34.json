{"ast": null, "code": "// This icon file is generated automatically.\nvar XOutlined = {\n  \"icon\": {\n    \"tag\": \"svg\",\n    \"attrs\": {\n      \"fill-rule\": \"evenodd\",\n      \"viewBox\": \"64 64 896 896\",\n      \"focusable\": \"false\"\n    },\n    \"children\": [{\n      \"tag\": \"path\",\n      \"attrs\": {\n        \"d\": \"M921 912L601.11 445.75l.55.43L890.08 112H793.7L558.74 384 372.15 112H119.37l298.65 435.31-.04-.04L103 912h96.39L460.6 609.38 668.2 912zM333.96 184.73l448.83 654.54H706.4L257.2 184.73z\"\n      }\n    }]\n  },\n  \"name\": \"x\",\n  \"theme\": \"outlined\"\n};\nexport default XOutlined;", "map": {"version": 3, "names": ["XOutlined"], "sources": ["C:/Users/<USER>/Desktop/Aptos/freelancer-escrow-frontend/node_modules/@ant-design/icons-svg/es/asn/XOutlined.js"], "sourcesContent": ["// This icon file is generated automatically.\nvar XOutlined = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"fill-rule\": \"evenodd\", \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M921 912L601.11 445.75l.55.43L890.08 112H793.7L558.74 384 372.15 112H119.37l298.65 435.31-.04-.04L103 912h96.39L460.6 609.38 668.2 912zM333.96 184.73l448.83 654.54H706.4L257.2 184.73z\" } }] }, \"name\": \"x\", \"theme\": \"outlined\" };\nexport default XOutlined;\n"], "mappings": "AAAA;AACA,IAAIA,SAAS,GAAG;EAAE,MAAM,EAAE;IAAE,KAAK,EAAE,KAAK;IAAE,OAAO,EAAE;MAAE,WAAW,EAAE,SAAS;MAAE,SAAS,EAAE,eAAe;MAAE,WAAW,EAAE;IAAQ,CAAC;IAAE,UAAU,EAAE,CAAC;MAAE,KAAK,EAAE,MAAM;MAAE,OAAO,EAAE;QAAE,GAAG,EAAE;MAA0L;IAAE,CAAC;EAAE,CAAC;EAAE,MAAM,EAAE,GAAG;EAAE,OAAO,EAAE;AAAW,CAAC;AACnZ,eAAeA,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}