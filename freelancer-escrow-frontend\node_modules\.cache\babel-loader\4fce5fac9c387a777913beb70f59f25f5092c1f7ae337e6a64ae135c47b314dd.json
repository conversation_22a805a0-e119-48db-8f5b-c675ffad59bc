{"ast": null, "code": "/** Name of the feature. */\nexport const StandardEvents = 'standard:events';\n/**\n * @deprecated Use {@link StandardEvents} instead.\n *\n * @group Deprecated\n */\nexport const Events = StandardEvents;", "map": {"version": 3, "names": ["StandardEvents", "Events"], "sources": ["C:\\Users\\<USER>\\Desktop\\Aptos\\freelancer-escrow-frontend\\node_modules\\@wallet-standard\\features\\src\\events.ts"], "sourcesContent": ["import type { Wallet } from '@wallet-standard/base';\n\n/** Name of the feature. */\nexport const StandardEvents = 'standard:events';\n/**\n * @deprecated Use {@link StandardEvents} instead.\n *\n * @group Deprecated\n */\nexport const Events = StandardEvents;\n\n/**\n * `standard:events` is a {@link \"@wallet-standard/base\".Wallet.features | feature} that may be implemented by a\n * {@link \"@wallet-standard/base\".Wallet} to allow the app to add an event listener and subscribe to events emitted by\n * the Wallet when properties of the Wallet {@link StandardEventsListeners.change}.\n *\n * @group Events\n */\nexport type StandardEventsFeature = {\n    /** Name of the feature. */\n    readonly [StandardEvents]: {\n        /** Version of the feature implemented by the {@link \"@wallet-standard/base\".Wallet}. */\n        readonly version: StandardEventsVersion;\n        /** Method to call to use the feature. */\n        readonly on: StandardEventsOnMethod;\n    };\n};\n/**\n * @deprecated Use {@link StandardEventsFeature} instead.\n *\n * @group Deprecated\n */\nexport type EventsFeature = StandardEventsFeature;\n\n/**\n * Version of the {@link StandardEventsFeature} implemented by a {@link \"@wallet-standard/base\".Wallet}.\n *\n * @group Events\n */\nexport type StandardEventsVersion = '1.0.0';\n/**\n * @deprecated Use {@link StandardEventsVersion} instead.\n *\n * @group Deprecated\n */\nexport type EventsVersion = StandardEventsVersion;\n\n/**\n * Method to call to use the {@link StandardEventsFeature}.\n *\n * @param event    Event type to listen for. {@link StandardEventsListeners.change | `change`} is the only event type.\n * @param listener Function that will be called when an event of the type is emitted.\n *\n * @return\n * `off` function which may be called to remove the event listener and unsubscribe from events.\n *\n * As with all event listeners, be careful to avoid memory leaks.\n *\n * @group Events\n */\nexport type StandardEventsOnMethod = <E extends StandardEventsNames>(\n    event: E,\n    listener: StandardEventsListeners[E]\n) => () => void;\n/**\n * @deprecated Use {@link StandardEventsOnMethod} instead.\n *\n * @group Deprecated\n */\nexport type EventsOnMethod = StandardEventsOnMethod;\n\n/**\n * Types of event listeners of the {@link StandardEventsFeature}.\n *\n * @group Events\n */\nexport interface StandardEventsListeners {\n    /**\n     * Listener that will be called when {@link StandardEventsChangeProperties | properties} of the\n     * {@link \"@wallet-standard/base\".Wallet} have changed.\n     *\n     * @param properties Properties that changed with their **new** values.\n     */\n    change(properties: StandardEventsChangeProperties): void;\n}\n/**\n * @deprecated Use {@link StandardEventsListeners} instead.\n *\n * @group Deprecated\n */\nexport type EventsListeners = StandardEventsListeners;\n\n/**\n * Names of {@link StandardEventsListeners} that can be listened for.\n *\n * @group Events\n */\nexport type StandardEventsNames = keyof StandardEventsListeners;\n/**\n * @deprecated Use {@link StandardEventsNames} instead.\n *\n * @group Deprecated\n */\nexport type EventsNames = StandardEventsNames;\n\n/**\n * Properties of a {@link \"@wallet-standard/base\".Wallet} that {@link StandardEventsListeners.change | changed} with their\n * **new** values.\n *\n * @group Events\n */\nexport interface StandardEventsChangeProperties {\n    /**\n     * {@link \"@wallet-standard/base\".Wallet.chains | Chains} supported by the Wallet.\n     *\n     * The Wallet should only define this field if the value of the property has changed.\n     *\n     * The value must be the **new** value of the property.\n     */\n    readonly chains?: Wallet['chains'];\n    /**\n     * {@link \"@wallet-standard/base\".Wallet.features | Features} supported by the Wallet.\n     *\n     * The Wallet should only define this field if the value of the property has changed.\n     *\n     * The value must be the **new** value of the property.\n     */\n    readonly features?: Wallet['features'];\n    /**\n     * {@link \"@wallet-standard/base\".Wallet.accounts | Accounts} that the app is authorized to use.\n     *\n     * The Wallet should only define this field if the value of the property has changed.\n     *\n     * The value must be the **new** value of the property.\n     */\n    readonly accounts?: Wallet['accounts'];\n}\n/**\n * @deprecated Use {@link StandardEventsChangeProperties} instead.\n *\n * @group Deprecated\n */\nexport type EventsChangeProperties = StandardEventsChangeProperties;\n"], "mappings": "AAEA;AACA,OAAO,MAAMA,cAAc,GAAG,iBAAiB;AAC/C;;;;;AAKA,OAAO,MAAMC,MAAM,GAAGD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}