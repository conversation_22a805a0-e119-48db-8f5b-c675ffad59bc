{"ast": null, "code": "export const streamChunk = function* (chunk, chunkSize) {\n  let len = chunk.byteLength;\n  if (!chunkSize || len < chunkSize) {\n    yield chunk;\n    return;\n  }\n  let pos = 0;\n  let end;\n  while (pos < len) {\n    end = pos + chunkSize;\n    yield chunk.slice(pos, end);\n    pos = end;\n  }\n};\nexport const readBytes = async function* (iterable, chunkSize, encode) {\n  for await (const chunk of iterable) {\n    yield* streamChunk(ArrayBuffer.isView(chunk) ? chunk : await encode(String(chunk)), chunkSize);\n  }\n};\nexport const trackStream = (stream, chunkSize, onProgress, onFinish, encode) => {\n  const iterator = readBytes(stream, chunkSize, encode);\n  let bytes = 0;\n  let done;\n  let _onFinish = e => {\n    if (!done) {\n      done = true;\n      onFinish && onFinish(e);\n    }\n  };\n  return new ReadableStream({\n    async pull(controller) {\n      try {\n        const {\n          done,\n          value\n        } = await iterator.next();\n        if (done) {\n          _onFinish();\n          controller.close();\n          return;\n        }\n        let len = value.byteLength;\n        if (onProgress) {\n          let loadedBytes = bytes += len;\n          onProgress(loadedBytes);\n        }\n        controller.enqueue(new Uint8Array(value));\n      } catch (err) {\n        _onFinish(err);\n        throw err;\n      }\n    },\n    cancel(reason) {\n      _onFinish(reason);\n      return iterator.return();\n    }\n  }, {\n    highWaterMark: 2\n  });\n};", "map": {"version": 3, "names": ["streamChunk", "chunk", "chunkSize", "len", "byteLength", "pos", "end", "slice", "readBytes", "iterable", "encode", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "String", "trackStream", "stream", "onProgress", "onFinish", "iterator", "bytes", "done", "_onFinish", "e", "ReadableStream", "pull", "controller", "value", "next", "close", "loadedBytes", "enqueue", "Uint8Array", "err", "cancel", "reason", "return", "highWaterMark"], "sources": ["C:/Users/<USER>/Desktop/Aptos/freelancer-escrow-frontend/node_modules/@aptos-labs/aptos-client/node_modules/axios/lib/helpers/trackStream.js"], "sourcesContent": ["\nexport const streamChunk = function* (chunk, chunkSize) {\n  let len = chunk.byteLength;\n\n  if (!chunkSize || len < chunkSize) {\n    yield chunk;\n    return;\n  }\n\n  let pos = 0;\n  let end;\n\n  while (pos < len) {\n    end = pos + chunkSize;\n    yield chunk.slice(pos, end);\n    pos = end;\n  }\n}\n\nexport const readBytes = async function* (iterable, chunkSize, encode) {\n  for await (const chunk of iterable) {\n    yield* streamChunk(ArrayBuffer.isView(chunk) ? chunk : (await encode(String(chunk))), chunkSize);\n  }\n}\n\nexport const trackStream = (stream, chunkSize, onProgress, onFinish, encode) => {\n  const iterator = readBytes(stream, chunkSize, encode);\n\n  let bytes = 0;\n  let done;\n  let _onFinish = (e) => {\n    if (!done) {\n      done = true;\n      onFinish && onFinish(e);\n    }\n  }\n\n  return new ReadableStream({\n    async pull(controller) {\n      try {\n        const {done, value} = await iterator.next();\n\n        if (done) {\n         _onFinish();\n          controller.close();\n          return;\n        }\n\n        let len = value.byteLength;\n        if (onProgress) {\n          let loadedBytes = bytes += len;\n          onProgress(loadedBytes);\n        }\n        controller.enqueue(new Uint8Array(value));\n      } catch (err) {\n        _onFinish(err);\n        throw err;\n      }\n    },\n    cancel(reason) {\n      _onFinish(reason);\n      return iterator.return();\n    }\n  }, {\n    highWaterMark: 2\n  })\n}\n"], "mappings": "AACA,OAAO,MAAMA,WAAW,GAAG,UAAAA,CAAWC,KAAK,EAAEC,SAAS,EAAE;EACtD,IAAIC,GAAG,GAAGF,KAAK,CAACG,UAAU;EAE1B,IAAI,CAACF,SAAS,IAAIC,GAAG,GAAGD,SAAS,EAAE;IACjC,MAAMD,KAAK;IACX;EACF;EAEA,IAAII,GAAG,GAAG,CAAC;EACX,IAAIC,GAAG;EAEP,OAAOD,GAAG,GAAGF,GAAG,EAAE;IAChBG,GAAG,GAAGD,GAAG,GAAGH,SAAS;IACrB,MAAMD,KAAK,CAACM,KAAK,CAACF,GAAG,EAAEC,GAAG,CAAC;IAC3BD,GAAG,GAAGC,GAAG;EACX;AACF,CAAC;AAED,OAAO,MAAME,SAAS,GAAG,gBAAAA,CAAiBC,QAAQ,EAAEP,SAAS,EAAEQ,MAAM,EAAE;EACrE,WAAW,MAAMT,KAAK,IAAIQ,QAAQ,EAAE;IAClC,OAAOT,WAAW,CAACW,WAAW,CAACC,MAAM,CAACX,KAAK,CAAC,GAAGA,KAAK,GAAI,MAAMS,MAAM,CAACG,MAAM,CAACZ,KAAK,CAAC,CAAE,EAAEC,SAAS,CAAC;EAClG;AACF,CAAC;AAED,OAAO,MAAMY,WAAW,GAAGA,CAACC,MAAM,EAAEb,SAAS,EAAEc,UAAU,EAAEC,QAAQ,EAAEP,MAAM,KAAK;EAC9E,MAAMQ,QAAQ,GAAGV,SAAS,CAACO,MAAM,EAAEb,SAAS,EAAEQ,MAAM,CAAC;EAErD,IAAIS,KAAK,GAAG,CAAC;EACb,IAAIC,IAAI;EACR,IAAIC,SAAS,GAAIC,CAAC,IAAK;IACrB,IAAI,CAACF,IAAI,EAAE;MACTA,IAAI,GAAG,IAAI;MACXH,QAAQ,IAAIA,QAAQ,CAACK,CAAC,CAAC;IACzB;EACF,CAAC;EAED,OAAO,IAAIC,cAAc,CAAC;IACxB,MAAMC,IAAIA,CAACC,UAAU,EAAE;MACrB,IAAI;QACF,MAAM;UAACL,IAAI;UAAEM;QAAK,CAAC,GAAG,MAAMR,QAAQ,CAACS,IAAI,CAAC,CAAC;QAE3C,IAAIP,IAAI,EAAE;UACTC,SAAS,CAAC,CAAC;UACVI,UAAU,CAACG,KAAK,CAAC,CAAC;UAClB;QACF;QAEA,IAAIzB,GAAG,GAAGuB,KAAK,CAACtB,UAAU;QAC1B,IAAIY,UAAU,EAAE;UACd,IAAIa,WAAW,GAAGV,KAAK,IAAIhB,GAAG;UAC9Ba,UAAU,CAACa,WAAW,CAAC;QACzB;QACAJ,UAAU,CAACK,OAAO,CAAC,IAAIC,UAAU,CAACL,KAAK,CAAC,CAAC;MAC3C,CAAC,CAAC,OAAOM,GAAG,EAAE;QACZX,SAAS,CAACW,GAAG,CAAC;QACd,MAAMA,GAAG;MACX;IACF,CAAC;IACDC,MAAMA,CAACC,MAAM,EAAE;MACbb,SAAS,CAACa,MAAM,CAAC;MACjB,OAAOhB,QAAQ,CAACiB,MAAM,CAAC,CAAC;IAC1B;EACF,CAAC,EAAE;IACDC,aAAa,EAAE;EACjB,CAAC,CAAC;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}