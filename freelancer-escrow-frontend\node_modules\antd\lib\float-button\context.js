"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = exports.FloatButtonGroupProvider = void 0;
var _react = _interopRequireDefault(require("react"));
const FloatButtonGroupContext = /*#__PURE__*/_react.default.createContext(undefined);
const {
  Provider: FloatButtonGroupProvider
} = FloatButtonGroupContext;
exports.FloatButtonGroupProvider = FloatButtonGroupProvider;
var _default = exports.default = FloatButtonGroupContext;