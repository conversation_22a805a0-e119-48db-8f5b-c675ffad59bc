{"ast": null, "code": "// This icon file is generated automatically.\nvar MobileOutlined = {\n  \"icon\": {\n    \"tag\": \"svg\",\n    \"attrs\": {\n      \"viewBox\": \"64 64 896 896\",\n      \"focusable\": \"false\"\n    },\n    \"children\": [{\n      \"tag\": \"path\",\n      \"attrs\": {\n        \"d\": \"M744 62H280c-35.3 0-64 28.7-64 64v768c0 35.3 28.7 64 64 64h464c35.3 0 64-28.7 64-64V126c0-35.3-28.7-64-64-64zm-8 824H288V134h448v752zM472 784a40 40 0 1080 0 40 40 0 10-80 0z\"\n      }\n    }]\n  },\n  \"name\": \"mobile\",\n  \"theme\": \"outlined\"\n};\nexport default MobileOutlined;", "map": {"version": 3, "names": ["MobileOutlined"], "sources": ["C:/Users/<USER>/Desktop/Aptos/freelancer-escrow-frontend/node_modules/@ant-design/icons-svg/es/asn/MobileOutlined.js"], "sourcesContent": ["// This icon file is generated automatically.\nvar MobileOutlined = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M744 62H280c-35.3 0-64 28.7-64 64v768c0 35.3 28.7 64 64 64h464c35.3 0 64-28.7 64-64V126c0-35.3-28.7-64-64-64zm-8 824H288V134h448v752zM472 784a40 40 0 1080 0 40 40 0 10-80 0z\" } }] }, \"name\": \"mobile\", \"theme\": \"outlined\" };\nexport default MobileOutlined;\n"], "mappings": "AAAA;AACA,IAAIA,cAAc,GAAG;EAAE,MAAM,EAAE;IAAE,KAAK,EAAE,KAAK;IAAE,OAAO,EAAE;MAAE,SAAS,EAAE,eAAe;MAAE,WAAW,EAAE;IAAQ,CAAC;IAAE,UAAU,EAAE,CAAC;MAAE,KAAK,EAAE,MAAM;MAAE,OAAO,EAAE;QAAE,GAAG,EAAE;MAAgL;IAAE,CAAC;EAAE,CAAC;EAAE,MAAM,EAAE,QAAQ;EAAE,OAAO,EAAE;AAAW,CAAC;AAC3X,eAAeA,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}