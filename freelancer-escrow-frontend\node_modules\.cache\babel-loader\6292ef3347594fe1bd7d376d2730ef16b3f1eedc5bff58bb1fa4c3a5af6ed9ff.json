{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nfunction composeProps(originProps, patchProps, isAll) {\n  var composedProps = _objectSpread(_objectSpread({}, originProps), isAll ? patchProps : {});\n  Object.keys(patchProps).forEach(function (key) {\n    var func = patchProps[key];\n    if (typeof func === 'function') {\n      composedProps[key] = function () {\n        var _originProps$key;\n        for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n          args[_key] = arguments[_key];\n        }\n        func.apply(void 0, args);\n        return (_originProps$key = originProps[key]) === null || _originProps$key === void 0 ? void 0 : _originProps$key.call.apply(_originProps$key, [originProps].concat(args));\n      };\n    }\n  });\n  return composedProps;\n}\nexport default composeProps;", "map": {"version": 3, "names": ["_objectSpread", "composeProps", "originProps", "patchProps", "isAll", "composedProps", "Object", "keys", "for<PERSON>ach", "key", "func", "_originProps$key", "_len", "arguments", "length", "args", "Array", "_key", "apply", "call", "concat"], "sources": ["C:/Users/<USER>/Desktop/Aptos/freelancer-escrow-frontend/node_modules/rc-util/es/composeProps.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nfunction composeProps(originProps, patchProps, isAll) {\n  var composedProps = _objectSpread(_objectSpread({}, originProps), isAll ? patchProps : {});\n  Object.keys(patchProps).forEach(function (key) {\n    var func = patchProps[key];\n    if (typeof func === 'function') {\n      composedProps[key] = function () {\n        var _originProps$key;\n        for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n          args[_key] = arguments[_key];\n        }\n        func.apply(void 0, args);\n        return (_originProps$key = originProps[key]) === null || _originProps$key === void 0 ? void 0 : _originProps$key.call.apply(_originProps$key, [originProps].concat(args));\n      };\n    }\n  });\n  return composedProps;\n}\nexport default composeProps;"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,SAASC,YAAYA,CAACC,WAAW,EAAEC,UAAU,EAAEC,KAAK,EAAE;EACpD,IAAIC,aAAa,GAAGL,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEE,WAAW,CAAC,EAAEE,KAAK,GAAGD,UAAU,GAAG,CAAC,CAAC,CAAC;EAC1FG,MAAM,CAACC,IAAI,CAACJ,UAAU,CAAC,CAACK,OAAO,CAAC,UAAUC,GAAG,EAAE;IAC7C,IAAIC,IAAI,GAAGP,UAAU,CAACM,GAAG,CAAC;IAC1B,IAAI,OAAOC,IAAI,KAAK,UAAU,EAAE;MAC9BL,aAAa,CAACI,GAAG,CAAC,GAAG,YAAY;QAC/B,IAAIE,gBAAgB;QACpB,KAAK,IAAIC,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACJ,IAAI,CAAC,EAAEK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,IAAI,EAAEK,IAAI,EAAE,EAAE;UACvFF,IAAI,CAACE,IAAI,CAAC,GAAGJ,SAAS,CAACI,IAAI,CAAC;QAC9B;QACAP,IAAI,CAACQ,KAAK,CAAC,KAAK,CAAC,EAAEH,IAAI,CAAC;QACxB,OAAO,CAACJ,gBAAgB,GAAGT,WAAW,CAACO,GAAG,CAAC,MAAM,IAAI,IAAIE,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,gBAAgB,CAACQ,IAAI,CAACD,KAAK,CAACP,gBAAgB,EAAE,CAACT,WAAW,CAAC,CAACkB,MAAM,CAACL,IAAI,CAAC,CAAC;MAC3K,CAAC;IACH;EACF,CAAC,CAAC;EACF,OAAOV,aAAa;AACtB;AACA,eAAeJ,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}