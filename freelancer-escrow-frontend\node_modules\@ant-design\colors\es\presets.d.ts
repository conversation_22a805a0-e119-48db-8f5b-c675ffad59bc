import type { Palette, PalettesProps } from './types';
export declare const presetPrimaryColors: Record<string, string>;
export declare const red: Palette;
export declare const volcano: Palette;
export declare const orange: Palette;
export declare const gold: Palette;
export declare const yellow: Palette;
export declare const lime: Palette;
export declare const green: Palette;
export declare const cyan: Palette;
export declare const blue: Palette;
export declare const geekblue: Palette;
export declare const purple: Palette;
export declare const magenta: Palette;
export declare const grey: Palette;
export declare const gray: Palette;
export declare const presetPalettes: PalettesProps;
export declare const redDark: Palette;
export declare const volcanoDark: Palette;
export declare const orangeDark: Palette;
export declare const goldDark: Palette;
export declare const yellowDark: Palette;
export declare const limeDark: Palette;
export declare const greenDark: Palette;
export declare const cyanDark: Palette;
export declare const blueDark: Palette;
export declare const geekblueDark: Palette;
export declare const purpleDark: Palette;
export declare const magentaDark: Palette;
export declare const greyDark: Palette;
export declare const presetDarkPalettes: PalettesProps;
//# sourceMappingURL=presets.d.ts.map