{"ast": null, "code": "/**\n * Test usage export. Do not use in your production\n */\nexport function isBodyOverflowing() {\n  return document.body.scrollHeight > (window.innerHeight || document.documentElement.clientHeight) && window.innerWidth > document.body.offsetWidth;\n}", "map": {"version": 3, "names": ["isBodyOverflowing", "document", "body", "scrollHeight", "window", "innerHeight", "documentElement", "clientHeight", "innerWidth", "offsetWidth"], "sources": ["C:/Users/<USER>/Desktop/Aptos/freelancer-escrow-frontend/node_modules/@rc-component/portal/es/util.js"], "sourcesContent": ["/**\n * Test usage export. Do not use in your production\n */\nexport function isBodyOverflowing() {\n  return document.body.scrollHeight > (window.innerHeight || document.documentElement.clientHeight) && window.innerWidth > document.body.offsetWidth;\n}"], "mappings": "AAAA;AACA;AACA;AACA,OAAO,SAASA,iBAAiBA,CAAA,EAAG;EAClC,OAAOC,QAAQ,CAACC,IAAI,CAACC,YAAY,IAAIC,MAAM,CAACC,WAAW,IAAIJ,QAAQ,CAACK,eAAe,CAACC,YAAY,CAAC,IAAIH,MAAM,CAACI,UAAU,GAAGP,QAAQ,CAACC,IAAI,CAACO,WAAW;AACpJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}