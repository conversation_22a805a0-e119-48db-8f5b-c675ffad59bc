import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import CloseCircleOutlinedSvg from "@ant-design/icons-svg/es/asn/CloseCircleOutlined";
import AntdIcon from "../components/AntdIcon";
var CloseCircleOutlined = function CloseCircleOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: CloseCircleOutlinedSvg
  }));
};

/**![close-circle](data:image/svg+xml;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************) */
var RefIcon = /*#__PURE__*/React.forwardRef(CloseCircleOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'CloseCircleOutlined';
}
export default RefIcon;