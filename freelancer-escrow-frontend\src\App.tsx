import { Button, Card, ConfigProvider, Space, theme, Typography } from 'antd';
import React from 'react';
import './App.css';

const { Title, Paragraph } = Typography;

const App: React.FC = () => {
  return (
    <ConfigProvider
      theme={{
        algorithm: theme.defaultAlgorithm,
        token: {
          colorPrimary: '#1890ff',
          borderRadius: 8,
        },
      }}
    >
      <div style={{ padding: '50px', textAlign: 'center' }}>
        <Card style={{ maxWidth: '800px', margin: '0 auto' }}>
          <Title level={1} style={{ color: '#1890ff' }}>
            FreelanceEscrow
          </Title>
          <Paragraph style={{ fontSize: '18px', marginBottom: '30px' }}>
            Secure freelance payments on Aptos blockchain
          </Paragraph>

          <Space direction="vertical" size="large">
            <div>
              <Title level={3}>🎉 Application Status</Title>
              <Paragraph>
                ✅ Backend API: Running on http://localhost:5000<br/>
                ✅ Frontend: Successfully compiled<br/>
                ✅ MongoDB: Connected and ready<br/>
                ✅ Smart Contracts: Deployed and tested
              </Paragraph>
            </div>

            <div>
              <Title level={3}>🚀 Features</Title>
              <Paragraph>
                • Secure escrow smart contracts on Aptos<br/>
                • User authentication and profiles<br/>
                • Project management dashboard<br/>
                • Real-time communication<br/>
                • Dispute resolution system
              </Paragraph>
            </div>

            <Space>
              <Button type="primary" size="large">
                Get Started
              </Button>
              <Button size="large">
                Learn More
              </Button>
            </Space>
          </Space>
        </Card>
      </div>
    </ConfigProvider>
  );
};

export default App;
