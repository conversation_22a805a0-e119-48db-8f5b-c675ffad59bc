{"ast": null, "code": "export {};", "map": {"version": 3, "names": [], "sources": ["C:\\Users\\<USER>\\Desktop\\Aptos\\freelancer-escrow-frontend\\node_modules\\@wallet-standard\\base\\src\\wallet.ts"], "sourcesContent": ["import type { ReadonlyUint8Array } from './bytes.js';\nimport type { IdentifierArray, IdentifierRecord, IdentifierString } from './identifier.js';\n\n/**\n * Version of the Wallet Standard implemented by a {@link Wallet}.\n *\n * Used by {@link Wallet.version | Wallet::version}.\n *\n * Note that this is _NOT_ a version of the Wallet, but a version of the Wallet Standard itself that the Wallet\n * supports.\n *\n * This may be used by the app to determine compatibility and feature detect.\n *\n * @group Wallet\n */\nexport type WalletVersion = '1.0.0';\n\n/**\n * A data URI containing a base64-encoded SVG, WebP, PNG, or GIF image.\n *\n * Used by {@link Wallet.icon | Wallet::icon} and {@link WalletAccount.icon | WalletAccount::icon}.\n *\n * @group Wallet\n */\nexport type WalletIcon = `data:image/${'svg+xml' | 'webp' | 'png' | 'gif'};base64,${string}`;\n\n/**\n * Interface of a **Wallet**, also referred to as a **Standard Wallet**.\n *\n * A Standard Wallet implements and adheres to the Wallet Standard.\n *\n * @group Wallet\n */\nexport interface Wallet {\n    /**\n     * {@link WalletVersion | Version} of the Wallet Standard implemented by the Wallet.\n     *\n     * Must be read-only, static, and canonically defined by the Wallet Standard.\n     */\n    readonly version: WalletVersion;\n\n    /**\n     * Name of the Wallet. This may be displayed by the app.\n     *\n     * Must be read-only, static, descriptive, unique, and canonically defined by the wallet extension or application.\n     */\n    readonly name: string;\n\n    /**\n     * {@link WalletIcon | Icon} of the Wallet. This may be displayed by the app.\n     *\n     * Must be read-only, static, and canonically defined by the wallet extension or application.\n     */\n    readonly icon: WalletIcon;\n\n    /**\n     * Chains supported by the Wallet.\n     *\n     * A **chain** is an {@link IdentifierString} which identifies a blockchain in a canonical, human-readable format.\n     * [CAIP-2](https://github.com/ChainAgnostic/CAIPs/blob/master/CAIPs/caip-2.md) chain IDs are compatible with this,\n     * but are not required to be used.\n     *\n     * Each blockchain should define its own **chains** by extension of the Wallet Standard, using its own namespace.\n     * The `standard` and `experimental` namespaces are reserved by the Wallet Standard.\n     *\n     * The {@link \"@wallet-standard/features\".EventsFeature | `standard:events` feature} should be used to notify the\n     * app if the value changes.\n     */\n    readonly chains: IdentifierArray;\n\n    /**\n     * Features supported by the Wallet.\n     *\n     * A **feature name** is an {@link IdentifierString} which identifies a **feature** in a canonical, human-readable\n     * format.\n     *\n     * Each blockchain should define its own features by extension of the Wallet Standard.\n     *\n     * The `standard` and `experimental` namespaces are reserved by the Wallet Standard.\n     *\n     * A **feature** may have any type. It may be a single method or value, or a collection of them.\n     *\n     * A **conventional feature** has the following structure:\n     *\n     * ```ts\n     *  export type ExperimentalEncryptFeature = {\n     *      // Name of the feature.\n     *      'experimental:encrypt': {\n     *          // Version of the feature.\n     *          version: '1.0.0';\n     *          // Properties of the feature.\n     *          ciphers: readonly 'x25519-xsalsa20-poly1305'[];\n     *          // Methods of the feature.\n     *          encrypt (data: Uint8Array): Promise<Uint8Array>;\n     *      };\n     *  };\n     * ```\n     *\n     * The {@link \"@wallet-standard/features\".EventsFeature | `standard:events` feature} should be used to notify the\n     * app if the value changes.\n     */\n    readonly features: IdentifierRecord<unknown>;\n\n    /**\n     * {@link WalletAccount | Accounts} that the app is authorized to use.\n     *\n     * This can be set by the Wallet so the app can use authorized accounts on the initial page load.\n     *\n     * The {@link \"@wallet-standard/features\".ConnectFeature | `standard:connect` feature} should be used to obtain\n     * authorization to the accounts.\n     *\n     * The {@link \"@wallet-standard/features\".EventsFeature | `standard:events` feature} should be used to notify the\n     * app if the value changes.\n     */\n    readonly accounts: readonly WalletAccount[];\n}\n\n/**\n * Interface of a **WalletAccount**, also referred to as an **Account**.\n *\n * An account is a _read-only data object_ that is provided from the Wallet to the app, authorizing the app to use it.\n *\n * The app can use an account to display and query information from a chain.\n *\n * The app can also act using an account by passing it to {@link Wallet.features | features} of the Wallet.\n *\n * Wallets may use or extend {@link \"@wallet-standard/wallet\".ReadonlyWalletAccount} which implements this interface.\n *\n * @group Wallet\n */\nexport interface WalletAccount {\n    /** Address of the account, corresponding with a public key. */\n    readonly address: string;\n\n    /** Public key of the account, corresponding with a secret key to use. */\n    readonly publicKey: ReadonlyUint8Array;\n\n    /**\n     * Chains supported by the account.\n     *\n     * This must be a subset of the {@link Wallet.chains | chains} of the Wallet.\n     */\n    readonly chains: IdentifierArray;\n\n    /**\n     * Feature names supported by the account.\n     *\n     * This must be a subset of the names of {@link Wallet.features | features} of the Wallet.\n     */\n    readonly features: IdentifierArray;\n\n    /** Optional user-friendly descriptive label or name for the account. This may be displayed by the app. */\n    readonly label?: string;\n\n    /** Optional user-friendly icon for the account. This may be displayed by the app. */\n    readonly icon?: WalletIcon;\n}\n\n/**\n * Helper type for defining a {@link Wallet} with a union or intersection of {@link Wallet.features | features}.\n *\n * @group Wallet\n */\nexport type WalletWithFeatures<Features extends Wallet['features']> = Omit<Wallet, 'features'> & {\n    features: Features;\n};\n"], "mappings": "", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}