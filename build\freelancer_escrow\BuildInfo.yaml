---
compiled_package_info:
  package_name: freelancer_escrow
  address_alias_instantiation:
    Extensions: "0000000000000000000000000000000000000000000000000000000000000001"
    aptos_framework: "0000000000000000000000000000000000000000000000000000000000000001"
    aptos_fungible_asset: 000000000000000000000000000000000000000000000000000000000000000a
    aptos_std: "0000000000000000000000000000000000000000000000000000000000000001"
    aptos_token: "0000000000000000000000000000000000000000000000000000000000000003"
    core_resources: 000000000000000000000000000000000000000000000000000000000a550c18
    freelancer_escrow: e5c9ba95ef610003dd37a51333f1e1c4017d7593f1351bd5292d87291a9b730a
    std: "0000000000000000000000000000000000000000000000000000000000000001"
    vm: "0000000000000000000000000000000000000000000000000000000000000000"
    vm_reserved: "0000000000000000000000000000000000000000000000000000000000000000"
  source_digest: C0FA2581C72B1A6A026CB3FB12941816744A3E933563B222CDE1A85189A8C723
  build_flags:
    dev_mode: true
    test_mode: true
    override_std: ~
    generate_docs: false
    generate_abis: false
    generate_move_model: true
    full_model_generation: true
    install_dir: ~
    force_recompilation: false
    additional_named_addresses: {}
    fetch_deps_only: false
    skip_fetch_latest_git_deps: false
    compiler_config:
      bytecode_version: 7
      known_attributes:
        - bytecode_instruction
        - deprecated
        - event
        - expected_failure
        - "fmt::skip"
        - legacy_entry_fun
        - "lint::allow_unsafe_randomness"
        - "lint::skip"
        - module_lock
        - "mutation::skip"
        - native_interface
        - persistent
        - randomness
        - resource_group
        - resource_group_member
        - test
        - test_only
        - verify_only
        - view
      skip_attribute_checks: false
      compiler_version: V2_0
      language_version: "2.1"
      experiments:
        - optimize=on
dependencies:
  - AptosFramework
  - AptosStdlib
  - MoveStdlib
bytecode_deps: []
