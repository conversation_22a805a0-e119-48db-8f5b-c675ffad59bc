import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import CloudUploadOutlinedSvg from "@ant-design/icons-svg/es/asn/CloudUploadOutlined";
import AntdIcon from "../components/AntdIcon";
var CloudUploadOutlined = function CloudUploadOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: CloudUploadOutlinedSvg
  }));
};

/**![cloud-upload](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxOC4zIDQ1OWE4IDggMCAwMC0xMi42IDBsLTExMiAxNDEuN2E3Ljk4IDcuOTggMCAwMDYuMyAxMi45aDczLjlWODU2YzAgNC40IDMuNiA4IDggOGg2MGM0LjQgMCA4LTMuNiA4LThWNjEzLjdINjI0YzYuNyAwIDEwLjQtNy43IDYuMy0xMi45TDUxOC4zIDQ1OXoiIC8+PHBhdGggZD0iTTgxMS40IDM2Ni43Qzc2NS42IDI0NS45IDY0OC45IDE2MCA1MTIuMiAxNjBTMjU4LjggMjQ1LjggMjEzIDM2Ni42QzEyNy4zIDM4OS4xIDY0IDQ2Ny4yIDY0IDU2MGMwIDExMC41IDg5LjUgMjAwIDE5OS45IDIwMEgzMDRjNC40IDAgOC0zLjYgOC04di02MGMwLTQuNC0zLjYtOC04LThoLTQwLjFjLTMzLjcgMC02NS40LTEzLjQtODktMzcuNy0yMy41LTI0LjItMzYtNTYuOC0zNC45LTkwLjYuOS0yNi40IDkuOS01MS4yIDI2LjItNzIuMSAxNi43LTIxLjMgNDAuMS0zNi44IDY2LjEtNDMuN2wzNy45LTkuOSAxMy45LTM2LjZjOC42LTIyLjggMjAuNi00NC4xIDM1LjctNjMuNGEyNDUuNiAyNDUuNiAwIDAxNTIuNC00OS45YzQxLjEtMjguOSA4OS41LTQ0LjIgMTQwLTQ0LjJzOTguOSAxNS4zIDE0MCA0NC4yYzE5LjkgMTQgMzcuNSAzMC44IDUyLjQgNDkuOSAxNS4xIDE5LjMgMjcuMSA0MC43IDM1LjcgNjMuNGwxMy44IDM2LjUgMzcuOCAxMEM4NDYuMSA0NTQuNSA4ODQgNTAzLjggODg0IDU2MGMwIDMzLjEtMTIuOSA2NC4zLTM2LjMgODcuN2ExMjMuMDcgMTIzLjA3IDAgMDEtODcuNiAzNi4zSDcyMGMtNC40IDAtOCAzLjYtOCA4djYwYzAgNC40IDMuNiA4IDggOGg0MC4xQzg3MC41IDc2MCA5NjAgNjcwLjUgOTYwIDU2MGMwLTkyLjctNjMuMS0xNzAuNy0xNDguNi0xOTMuM3oiIC8+PC9zdmc+) */
var RefIcon = /*#__PURE__*/React.forwardRef(CloudUploadOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'CloudUploadOutlined';
}
export default RefIcon;