import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import CompassOutlinedSvg from "@ant-design/icons-svg/es/asn/CompassOutlined";
import AntdIcon from "../components/AntdIcon";
var CompassOutlined = function CompassOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: CompassOutlinedSvg
  }));
};

/**![compass](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMiA2NEMyNjQuNiA2NCA2NCAyNjQuNiA2NCA1MTJzMjAwLjYgNDQ4IDQ0OCA0NDggNDQ4LTIwMC42IDQ0OC00NDhTNzU5LjQgNjQgNTEyIDY0em0wIDgyMGMtMjA1LjQgMC0zNzItMTY2LjYtMzcyLTM3MnMxNjYuNi0zNzIgMzcyLTM3MiAzNzIgMTY2LjYgMzcyIDM3Mi0xNjYuNiAzNzItMzcyIDM3MnptMTk4LjQtNTg4LjFhMzIgMzIgMCAwMC0yNC41LjVMNDE0LjkgNDE1IDI5Ni40IDY4NmMtMy42IDguMi0zLjYgMTcuNSAwIDI1LjcgMy40IDcuOCA5LjcgMTMuOSAxNy43IDE3IDMuOCAxLjUgNy43IDIuMiAxMS43IDIuMiA0LjQgMCA4LjctLjkgMTIuOC0yLjdsMjcxLTExOC42IDExOC41LTI3MWEzMi4wNiAzMi4wNiAwIDAwLTE3LjctNDIuN3pNNTc2LjggNTM0LjRsMjYuMiAyNi4yLTQyLjQgNDIuNC0yNi4yLTI2LjJMMzgwIDY0NC40IDQ0Ny41IDQ5MCA0MjIgNDY0LjRsNDIuNC00Mi40IDI1LjUgMjUuNUw2NDQuNCAzODBsLTY3LjYgMTU0LjR6TTQ2NC40IDQyMkw0MjIgNDY0LjRsMjUuNSAyNS42IDg2LjkgODYuOCAyNi4yIDI2LjIgNDIuNC00Mi40LTI2LjItMjYuMi04Ni44LTg2Ljl6IiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(CompassOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'CompassOutlined';
}
export default RefIcon;