import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import CarryOutOutlinedSvg from "@ant-design/icons-svg/es/asn/CarryOutOutlined";
import AntdIcon from "../components/AntdIcon";
var CarryOutOutlined = function CarryOutOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: CarryOutOutlinedSvg
  }));
};

/**![carry-out](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg4MCAxODRINzEydi02NGMwLTQuNC0zLjYtOC04LThoLTU2Yy00LjQgMC04IDMuNi04IDh2NjRIMzg0di02NGMwLTQuNC0zLjYtOC04LThoLTU2Yy00LjQgMC04IDMuNi04IDh2NjRIMTQ0Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnY2NjRjMCAxNy43IDE0LjMgMzIgMzIgMzJoNzM2YzE3LjcgMCAzMi0xNC4zIDMyLTMyVjIxNmMwLTE3LjctMTQuMy0zMi0zMi0zMnptLTQwIDY1NkgxODRWMjU2aDEyOHY0OGMwIDQuNCAzLjYgOCA4IDhoNTZjNC40IDAgOC0zLjYgOC04di00OGgyNTZ2NDhjMCA0LjQgMy42IDggOCA4aDU2YzQuNCAwIDgtMy42IDgtOHYtNDhoMTI4djU4NHpNNjg4IDQyMGgtNTUuMmMtNS4xIDAtMTAgMi41LTEzIDYuNkw0NjguOSA2MzQuNGwtNjQuNy04OWMtMy00LjEtNy44LTYuNi0xMy02LjZIMzM2Yy02LjUgMC0xMC4zIDcuNC02LjUgMTIuN2wxMjYuNCAxNzRhMTYuMSAxNi4xIDAgMDAyNiAwbDIxMi42LTI5Mi43YzMuOC01LjQgMC0xMi44LTYuNS0xMi44eiIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/React.forwardRef(CarryOutOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'CarryOutOutlined';
}
export default RefIcon;