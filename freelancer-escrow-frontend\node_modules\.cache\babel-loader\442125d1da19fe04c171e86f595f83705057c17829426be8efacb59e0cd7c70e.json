{"ast": null, "code": "// https://github.com/kevva/url-regex/blob/master/index.js\nvar urlReg;\nexport default (function () {\n  if (urlReg) {\n    return urlReg;\n  }\n  var word = '[a-fA-F\\\\d:]';\n  var b = function b(options) {\n    return options && options.includeBoundaries ? \"(?:(?<=\\\\s|^)(?=\".concat(word, \")|(?<=\").concat(word, \")(?=\\\\s|$))\") : '';\n  };\n  var v4 = '(?:25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]\\\\d|\\\\d)(?:\\\\.(?:25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]\\\\d|\\\\d)){3}';\n  var v6seg = '[a-fA-F\\\\d]{1,4}';\n  var v6List = [\"(?:\".concat(v6seg, \":){7}(?:\").concat(v6seg, \"|:)\"),\n  // 1:2:3:4:5:6:7::  1:2:3:4:5:6:7:8\n  \"(?:\".concat(v6seg, \":){6}(?:\").concat(v4, \"|:\").concat(v6seg, \"|:)\"),\n  // 1:2:3:4:5:6::    1:2:3:4:5:6::8   1:2:3:4:5:6::8  1:2:3:4:5:6::\n  \"(?:\".concat(v6seg, \":){5}(?::\").concat(v4, \"|(?::\").concat(v6seg, \"){1,2}|:)\"),\n  // 1:2:3:4:5::      1:2:3:4:5::7:8   1:2:3:4:5::8    1:2:3:4:5::\n  \"(?:\".concat(v6seg, \":){4}(?:(?::\").concat(v6seg, \"){0,1}:\").concat(v4, \"|(?::\").concat(v6seg, \"){1,3}|:)\"),\n  // 1:2:3:4::        1:2:3:4::6:7:8   1:2:3:4::8      1:2:3:4::\n  \"(?:\".concat(v6seg, \":){3}(?:(?::\").concat(v6seg, \"){0,2}:\").concat(v4, \"|(?::\").concat(v6seg, \"){1,4}|:)\"),\n  // 1:2:3::          1:2:3::5:6:7:8   1:2:3::8        1:2:3::\n  \"(?:\".concat(v6seg, \":){2}(?:(?::\").concat(v6seg, \"){0,3}:\").concat(v4, \"|(?::\").concat(v6seg, \"){1,5}|:)\"),\n  // 1:2::            1:2::4:5:6:7:8   1:2::8          1:2::\n  \"(?:\".concat(v6seg, \":){1}(?:(?::\").concat(v6seg, \"){0,4}:\").concat(v4, \"|(?::\").concat(v6seg, \"){1,6}|:)\"),\n  // 1::              1::3:4:5:6:7:8   1::8            1::\n  \"(?::(?:(?::\".concat(v6seg, \"){0,5}:\").concat(v4, \"|(?::\").concat(v6seg, \"){1,7}|:))\") // ::2:3:4:5:6:7:8  ::2:3:4:5:6:7:8  ::8             ::\n  ];\n  var v6Eth0 = \"(?:%[0-9a-zA-Z]{1,})?\"; // %eth0            %1\n\n  var v6 = \"(?:\".concat(v6List.join('|'), \")\").concat(v6Eth0);\n\n  // Pre-compile only the exact regexes because adding a global flag make regexes stateful\n  var v46Exact = new RegExp(\"(?:^\".concat(v4, \"$)|(?:^\").concat(v6, \"$)\"));\n  var v4exact = new RegExp(\"^\".concat(v4, \"$\"));\n  var v6exact = new RegExp(\"^\".concat(v6, \"$\"));\n  var ip = function ip(options) {\n    return options && options.exact ? v46Exact : new RegExp(\"(?:\".concat(b(options)).concat(v4).concat(b(options), \")|(?:\").concat(b(options)).concat(v6).concat(b(options), \")\"), 'g');\n  };\n  ip.v4 = function (options) {\n    return options && options.exact ? v4exact : new RegExp(\"\".concat(b(options)).concat(v4).concat(b(options)), 'g');\n  };\n  ip.v6 = function (options) {\n    return options && options.exact ? v6exact : new RegExp(\"\".concat(b(options)).concat(v6).concat(b(options)), 'g');\n  };\n  var protocol = \"(?:(?:[a-z]+:)?//)\";\n  var auth = '(?:\\\\S+(?::\\\\S*)?@)?';\n  var ipv4 = ip.v4().source;\n  var ipv6 = ip.v6().source;\n  var host = \"(?:(?:[a-z\\\\u00a1-\\\\uffff0-9][-_]*)*[a-z\\\\u00a1-\\\\uffff0-9]+)\";\n  var domain = \"(?:\\\\.(?:[a-z\\\\u00a1-\\\\uffff0-9]-*)*[a-z\\\\u00a1-\\\\uffff0-9]+)*\";\n  var tld = \"(?:\\\\.(?:[a-z\\\\u00a1-\\\\uffff]{2,}))\";\n  var port = '(?::\\\\d{2,5})?';\n  var path = '(?:[/?#][^\\\\s\"]*)?';\n  var regex = \"(?:\".concat(protocol, \"|www\\\\.)\").concat(auth, \"(?:localhost|\").concat(ipv4, \"|\").concat(ipv6, \"|\").concat(host).concat(domain).concat(tld, \")\").concat(port).concat(path);\n  urlReg = new RegExp(\"(?:^\".concat(regex, \"$)\"), 'i');\n  return urlReg;\n});", "map": {"version": 3, "names": ["urlReg", "word", "b", "options", "includeBoundaries", "concat", "v4", "v6seg", "v6List", "v6Eth0", "v6", "join", "v46Exact", "RegExp", "v4exact", "v6exact", "ip", "exact", "protocol", "auth", "ipv4", "source", "ipv6", "host", "domain", "tld", "port", "path", "regex"], "sources": ["C:/Users/<USER>/Desktop/Aptos/freelancer-escrow-frontend/node_modules/@rc-component/async-validator/es/rule/url.js"], "sourcesContent": ["// https://github.com/kevva/url-regex/blob/master/index.js\nvar urlReg;\nexport default (function () {\n  if (urlReg) {\n    return urlReg;\n  }\n  var word = '[a-fA-F\\\\d:]';\n  var b = function b(options) {\n    return options && options.includeBoundaries ? \"(?:(?<=\\\\s|^)(?=\".concat(word, \")|(?<=\").concat(word, \")(?=\\\\s|$))\") : '';\n  };\n  var v4 = '(?:25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]\\\\d|\\\\d)(?:\\\\.(?:25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]\\\\d|\\\\d)){3}';\n  var v6seg = '[a-fA-F\\\\d]{1,4}';\n  var v6List = [\"(?:\".concat(v6seg, \":){7}(?:\").concat(v6seg, \"|:)\"), // 1:2:3:4:5:6:7::  1:2:3:4:5:6:7:8\n  \"(?:\".concat(v6seg, \":){6}(?:\").concat(v4, \"|:\").concat(v6seg, \"|:)\"), // 1:2:3:4:5:6::    1:2:3:4:5:6::8   1:2:3:4:5:6::8  1:2:3:4:5:6::\n  \"(?:\".concat(v6seg, \":){5}(?::\").concat(v4, \"|(?::\").concat(v6seg, \"){1,2}|:)\"), // 1:2:3:4:5::      1:2:3:4:5::7:8   1:2:3:4:5::8    1:2:3:4:5::\n  \"(?:\".concat(v6seg, \":){4}(?:(?::\").concat(v6seg, \"){0,1}:\").concat(v4, \"|(?::\").concat(v6seg, \"){1,3}|:)\"), // 1:2:3:4::        1:2:3:4::6:7:8   1:2:3:4::8      1:2:3:4::\n  \"(?:\".concat(v6seg, \":){3}(?:(?::\").concat(v6seg, \"){0,2}:\").concat(v4, \"|(?::\").concat(v6seg, \"){1,4}|:)\"), // 1:2:3::          1:2:3::5:6:7:8   1:2:3::8        1:2:3::\n  \"(?:\".concat(v6seg, \":){2}(?:(?::\").concat(v6seg, \"){0,3}:\").concat(v4, \"|(?::\").concat(v6seg, \"){1,5}|:)\"), // 1:2::            1:2::4:5:6:7:8   1:2::8          1:2::\n  \"(?:\".concat(v6seg, \":){1}(?:(?::\").concat(v6seg, \"){0,4}:\").concat(v4, \"|(?::\").concat(v6seg, \"){1,6}|:)\"), // 1::              1::3:4:5:6:7:8   1::8            1::\n  \"(?::(?:(?::\".concat(v6seg, \"){0,5}:\").concat(v4, \"|(?::\").concat(v6seg, \"){1,7}|:))\") // ::2:3:4:5:6:7:8  ::2:3:4:5:6:7:8  ::8             ::\n  ];\n  var v6Eth0 = \"(?:%[0-9a-zA-Z]{1,})?\"; // %eth0            %1\n\n  var v6 = \"(?:\".concat(v6List.join('|'), \")\").concat(v6Eth0);\n\n  // Pre-compile only the exact regexes because adding a global flag make regexes stateful\n  var v46Exact = new RegExp(\"(?:^\".concat(v4, \"$)|(?:^\").concat(v6, \"$)\"));\n  var v4exact = new RegExp(\"^\".concat(v4, \"$\"));\n  var v6exact = new RegExp(\"^\".concat(v6, \"$\"));\n  var ip = function ip(options) {\n    return options && options.exact ? v46Exact : new RegExp(\"(?:\".concat(b(options)).concat(v4).concat(b(options), \")|(?:\").concat(b(options)).concat(v6).concat(b(options), \")\"), 'g');\n  };\n  ip.v4 = function (options) {\n    return options && options.exact ? v4exact : new RegExp(\"\".concat(b(options)).concat(v4).concat(b(options)), 'g');\n  };\n  ip.v6 = function (options) {\n    return options && options.exact ? v6exact : new RegExp(\"\".concat(b(options)).concat(v6).concat(b(options)), 'g');\n  };\n  var protocol = \"(?:(?:[a-z]+:)?//)\";\n  var auth = '(?:\\\\S+(?::\\\\S*)?@)?';\n  var ipv4 = ip.v4().source;\n  var ipv6 = ip.v6().source;\n  var host = \"(?:(?:[a-z\\\\u00a1-\\\\uffff0-9][-_]*)*[a-z\\\\u00a1-\\\\uffff0-9]+)\";\n  var domain = \"(?:\\\\.(?:[a-z\\\\u00a1-\\\\uffff0-9]-*)*[a-z\\\\u00a1-\\\\uffff0-9]+)*\";\n  var tld = \"(?:\\\\.(?:[a-z\\\\u00a1-\\\\uffff]{2,}))\";\n  var port = '(?::\\\\d{2,5})?';\n  var path = '(?:[/?#][^\\\\s\"]*)?';\n  var regex = \"(?:\".concat(protocol, \"|www\\\\.)\").concat(auth, \"(?:localhost|\").concat(ipv4, \"|\").concat(ipv6, \"|\").concat(host).concat(domain).concat(tld, \")\").concat(port).concat(path);\n  urlReg = new RegExp(\"(?:^\".concat(regex, \"$)\"), 'i');\n  return urlReg;\n});"], "mappings": "AAAA;AACA,IAAIA,MAAM;AACV,gBAAgB,YAAY;EAC1B,IAAIA,MAAM,EAAE;IACV,OAAOA,MAAM;EACf;EACA,IAAIC,IAAI,GAAG,cAAc;EACzB,IAAIC,CAAC,GAAG,SAASA,CAACA,CAACC,OAAO,EAAE;IAC1B,OAAOA,OAAO,IAAIA,OAAO,CAACC,iBAAiB,GAAG,kBAAkB,CAACC,MAAM,CAACJ,IAAI,EAAE,QAAQ,CAAC,CAACI,MAAM,CAACJ,IAAI,EAAE,aAAa,CAAC,GAAG,EAAE;EAC1H,CAAC;EACD,IAAIK,EAAE,GAAG,gGAAgG;EACzG,IAAIC,KAAK,GAAG,kBAAkB;EAC9B,IAAIC,MAAM,GAAG,CAAC,KAAK,CAACH,MAAM,CAACE,KAAK,EAAE,UAAU,CAAC,CAACF,MAAM,CAACE,KAAK,EAAE,KAAK,CAAC;EAAE;EACpE,KAAK,CAACF,MAAM,CAACE,KAAK,EAAE,UAAU,CAAC,CAACF,MAAM,CAACC,EAAE,EAAE,IAAI,CAAC,CAACD,MAAM,CAACE,KAAK,EAAE,KAAK,CAAC;EAAE;EACvE,KAAK,CAACF,MAAM,CAACE,KAAK,EAAE,WAAW,CAAC,CAACF,MAAM,CAACC,EAAE,EAAE,OAAO,CAAC,CAACD,MAAM,CAACE,KAAK,EAAE,WAAW,CAAC;EAAE;EACjF,KAAK,CAACF,MAAM,CAACE,KAAK,EAAE,cAAc,CAAC,CAACF,MAAM,CAACE,KAAK,EAAE,SAAS,CAAC,CAACF,MAAM,CAACC,EAAE,EAAE,OAAO,CAAC,CAACD,MAAM,CAACE,KAAK,EAAE,WAAW,CAAC;EAAE;EAC7G,KAAK,CAACF,MAAM,CAACE,KAAK,EAAE,cAAc,CAAC,CAACF,MAAM,CAACE,KAAK,EAAE,SAAS,CAAC,CAACF,MAAM,CAACC,EAAE,EAAE,OAAO,CAAC,CAACD,MAAM,CAACE,KAAK,EAAE,WAAW,CAAC;EAAE;EAC7G,KAAK,CAACF,MAAM,CAACE,KAAK,EAAE,cAAc,CAAC,CAACF,MAAM,CAACE,KAAK,EAAE,SAAS,CAAC,CAACF,MAAM,CAACC,EAAE,EAAE,OAAO,CAAC,CAACD,MAAM,CAACE,KAAK,EAAE,WAAW,CAAC;EAAE;EAC7G,KAAK,CAACF,MAAM,CAACE,KAAK,EAAE,cAAc,CAAC,CAACF,MAAM,CAACE,KAAK,EAAE,SAAS,CAAC,CAACF,MAAM,CAACC,EAAE,EAAE,OAAO,CAAC,CAACD,MAAM,CAACE,KAAK,EAAE,WAAW,CAAC;EAAE;EAC7G,aAAa,CAACF,MAAM,CAACE,KAAK,EAAE,SAAS,CAAC,CAACF,MAAM,CAACC,EAAE,EAAE,OAAO,CAAC,CAACD,MAAM,CAACE,KAAK,EAAE,YAAY,CAAC,CAAC;EAAA,CACtF;EACD,IAAIE,MAAM,GAAG,uBAAuB,CAAC,CAAC;;EAEtC,IAAIC,EAAE,GAAG,KAAK,CAACL,MAAM,CAACG,MAAM,CAACG,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,CAACN,MAAM,CAACI,MAAM,CAAC;;EAE3D;EACA,IAAIG,QAAQ,GAAG,IAAIC,MAAM,CAAC,MAAM,CAACR,MAAM,CAACC,EAAE,EAAE,SAAS,CAAC,CAACD,MAAM,CAACK,EAAE,EAAE,IAAI,CAAC,CAAC;EACxE,IAAII,OAAO,GAAG,IAAID,MAAM,CAAC,GAAG,CAACR,MAAM,CAACC,EAAE,EAAE,GAAG,CAAC,CAAC;EAC7C,IAAIS,OAAO,GAAG,IAAIF,MAAM,CAAC,GAAG,CAACR,MAAM,CAACK,EAAE,EAAE,GAAG,CAAC,CAAC;EAC7C,IAAIM,EAAE,GAAG,SAASA,EAAEA,CAACb,OAAO,EAAE;IAC5B,OAAOA,OAAO,IAAIA,OAAO,CAACc,KAAK,GAAGL,QAAQ,GAAG,IAAIC,MAAM,CAAC,KAAK,CAACR,MAAM,CAACH,CAAC,CAACC,OAAO,CAAC,CAAC,CAACE,MAAM,CAACC,EAAE,CAAC,CAACD,MAAM,CAACH,CAAC,CAACC,OAAO,CAAC,EAAE,OAAO,CAAC,CAACE,MAAM,CAACH,CAAC,CAACC,OAAO,CAAC,CAAC,CAACE,MAAM,CAACK,EAAE,CAAC,CAACL,MAAM,CAACH,CAAC,CAACC,OAAO,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC;EACrL,CAAC;EACDa,EAAE,CAACV,EAAE,GAAG,UAAUH,OAAO,EAAE;IACzB,OAAOA,OAAO,IAAIA,OAAO,CAACc,KAAK,GAAGH,OAAO,GAAG,IAAID,MAAM,CAAC,EAAE,CAACR,MAAM,CAACH,CAAC,CAACC,OAAO,CAAC,CAAC,CAACE,MAAM,CAACC,EAAE,CAAC,CAACD,MAAM,CAACH,CAAC,CAACC,OAAO,CAAC,CAAC,EAAE,GAAG,CAAC;EAClH,CAAC;EACDa,EAAE,CAACN,EAAE,GAAG,UAAUP,OAAO,EAAE;IACzB,OAAOA,OAAO,IAAIA,OAAO,CAACc,KAAK,GAAGF,OAAO,GAAG,IAAIF,MAAM,CAAC,EAAE,CAACR,MAAM,CAACH,CAAC,CAACC,OAAO,CAAC,CAAC,CAACE,MAAM,CAACK,EAAE,CAAC,CAACL,MAAM,CAACH,CAAC,CAACC,OAAO,CAAC,CAAC,EAAE,GAAG,CAAC;EAClH,CAAC;EACD,IAAIe,QAAQ,GAAG,oBAAoB;EACnC,IAAIC,IAAI,GAAG,sBAAsB;EACjC,IAAIC,IAAI,GAAGJ,EAAE,CAACV,EAAE,CAAC,CAAC,CAACe,MAAM;EACzB,IAAIC,IAAI,GAAGN,EAAE,CAACN,EAAE,CAAC,CAAC,CAACW,MAAM;EACzB,IAAIE,IAAI,GAAG,+DAA+D;EAC1E,IAAIC,MAAM,GAAG,gEAAgE;EAC7E,IAAIC,GAAG,GAAG,qCAAqC;EAC/C,IAAIC,IAAI,GAAG,gBAAgB;EAC3B,IAAIC,IAAI,GAAG,oBAAoB;EAC/B,IAAIC,KAAK,GAAG,KAAK,CAACvB,MAAM,CAACa,QAAQ,EAAE,UAAU,CAAC,CAACb,MAAM,CAACc,IAAI,EAAE,eAAe,CAAC,CAACd,MAAM,CAACe,IAAI,EAAE,GAAG,CAAC,CAACf,MAAM,CAACiB,IAAI,EAAE,GAAG,CAAC,CAACjB,MAAM,CAACkB,IAAI,CAAC,CAAClB,MAAM,CAACmB,MAAM,CAAC,CAACnB,MAAM,CAACoB,GAAG,EAAE,GAAG,CAAC,CAACpB,MAAM,CAACqB,IAAI,CAAC,CAACrB,MAAM,CAACsB,IAAI,CAAC;EACvL3B,MAAM,GAAG,IAAIa,MAAM,CAAC,MAAM,CAACR,MAAM,CAACuB,KAAK,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC;EACpD,OAAO5B,MAAM;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}