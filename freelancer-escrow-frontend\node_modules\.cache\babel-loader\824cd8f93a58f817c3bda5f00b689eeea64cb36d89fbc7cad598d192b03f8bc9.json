{"ast": null, "code": "export {};", "map": {"version": 3, "names": [], "sources": ["C:\\Users\\<USER>\\Desktop\\Aptos\\freelancer-escrow-frontend\\node_modules\\@wallet-standard\\base\\src\\window.ts"], "sourcesContent": ["import type { Wallet } from './wallet.js';\n\n/**\n * Global `window` type for dispatching and listening for {@link WindowAppReadyEvent} and {@link WindowRegisterWalletEvent}.\n *\n * ```ts\n * import { WalletEventsWindow } from '@wallet-standard/base';\n *\n * declare const window: WalletEventsWindow;\n * // OR\n * (window as WalletEventsWindow)\n * ```\n *\n * @group Window\n */\nexport interface WalletEventsWindow extends Omit<Window, 'addEventListener' | 'dispatchEvent'> {\n    /** Add a listener for {@link WindowAppReadyEvent}. */\n    addEventListener(type: WindowAppReadyEventType, listener: (event: WindowAppReadyEvent) => void): void;\n    /** Add a listener for {@link WindowRegisterWalletEvent}. */\n    addEventListener(type: WindowRegisterWalletEventType, listener: (event: WindowRegisterWalletEvent) => void): void;\n    /** Dispatch a {@link WindowAppReadyEvent}. */\n    dispatchEvent(event: WindowAppReadyEvent): void;\n    /** Dispatch a {@link WindowRegisterWalletEvent}. */\n    dispatchEvent(event: WindowRegisterWalletEvent): void;\n}\n\n/**\n * Type of {@link WindowAppReadyEvent}.\n *\n * @group App Ready Event\n */\nexport type WindowAppReadyEventType = 'wallet-standard:app-ready';\n\n/** Interface that will be provided to {@link Wallet | Wallets} by the app when the app calls the\n * {@link WindowRegisterWalletEventCallback} provided by Wallets.\n *\n * Wallets must call the {@link WindowAppReadyEventAPI.register | register} method to register themselves.\n *\n * @group App Ready Event\n */\nexport interface WindowAppReadyEventAPI {\n    /**\n     * Register a {@link Wallet} with the app.\n     *\n     * @return\n     * `unregister` function to programmatically unregister the Wallet.\n     *\n     * Wallets generally do not need to, and should not, call this.\n     */\n    register(wallet: Wallet): () => void;\n}\n\n/**\n * Event that will be dispatched by the app on the `window` when the app is ready to register {@link Wallet | Wallets}.\n *\n * Wallets must listen for this event, and {@link WindowAppReadyEventAPI.register register} themselves when the event is\n * dispatched.\n *\n * @group App Ready Event\n */\nexport type WindowAppReadyEvent = UnstoppableCustomEvent<WindowAppReadyEventType, WindowAppReadyEventAPI>;\n\n/**\n * Type of {@link WindowRegisterWalletEvent}.\n *\n * @group Register Wallet Event\n */\nexport type WindowRegisterWalletEventType = 'wallet-standard:register-wallet';\n\n/**\n * Callback function provided by {@link Wallet | Wallets} to be called by the app when the app is ready to register\n * Wallets.\n *\n * @group Register Wallet Event\n */\nexport type WindowRegisterWalletEventCallback = (api: WindowAppReadyEventAPI) => void;\n\n/**\n * Event that will be dispatched on the `window` by each {@link Wallet | Wallet} when the Wallet is ready to be\n * registered by the app.\n *\n * The app must listen for this event, and register Wallets when the event is dispatched.\n *\n * @group Register Wallet Event\n */\nexport type WindowRegisterWalletEvent = UnstoppableCustomEvent<\n    WindowRegisterWalletEventType,\n    WindowRegisterWalletEventCallback\n>;\n\n/**\n * @deprecated Use {@link WalletEventsWindow} instead.\n *\n * @group Deprecated\n */\nexport interface DEPRECATED_WalletsWindow extends Window {\n    navigator: DEPRECATED_WalletsNavigator;\n}\n\n/**\n * @deprecated Use {@link WalletEventsWindow} instead.\n *\n * @group Deprecated\n */\nexport interface DEPRECATED_WalletsNavigator extends Navigator {\n    wallets?: DEPRECATED_Wallets;\n}\n\n/**\n * @deprecated Use {@link WalletEventsWindow} instead.\n *\n * @group Deprecated\n */\nexport interface DEPRECATED_Wallets {\n    push(...callbacks: DEPRECATED_WalletsCallback[]): void;\n}\n\n/**\n * @deprecated Use {@link WalletEventsWindow} instead.\n *\n * @group Deprecated\n */\nexport type DEPRECATED_WalletsCallback = (wallets: { register(...wallets: Wallet[]): () => void }) => void;\n\n/**\n * @internal\n *\n * A custom event that cannot have its default behavior prevented or its propagation stopped.\n *\n * This is an internal type, extended by {@link WindowAppReadyEvent} and {@link WindowRegisterWalletEvent}.\n *\n * [`window.CustomEvent`](https://developer.mozilla.org/en-US/docs/Web/API/CustomEvent/CustomEvent) is not used because\n * Node.js doesn't have it, but this interface is compatible with it.\n *\n * @group Internal\n */\nexport interface UnstoppableCustomEvent<T extends string, D> extends Event {\n    /** Type of the event. */\n    readonly type: T;\n    /** Data attached to the event. */\n    readonly detail: D;\n    /** @deprecated Does nothing and throws an error if called. */\n    preventDefault(): never;\n    /** @deprecated Does nothing and throws an error if called. */\n    stopImmediatePropagation(): never;\n    /** @deprecated Does nothing and throws an error if called. */\n    stopPropagation(): never;\n}\n"], "mappings": "", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}