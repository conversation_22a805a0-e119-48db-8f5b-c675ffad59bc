{"ast": null, "code": "var __classPrivateFieldGet = this && this.__classPrivateFieldGet || function (receiver, state, kind, f) {\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n  return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n};\nvar __classPrivateFieldSet = this && this.__classPrivateFieldSet || function (receiver, state, value, kind, f) {\n  if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n  return kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value), value;\n};\nvar _ReadonlyWalletAccount_address, _ReadonlyWalletAccount_publicKey, _ReadonlyWalletAccount_chains, _ReadonlyWalletAccount_features, _ReadonlyWalletAccount_label, _ReadonlyWalletAccount_icon;\n/**\n * Base implementation of a {@link \"@wallet-standard/base\".WalletAccount} to be used or extended by a\n * {@link \"@wallet-standard/base\".Wallet}.\n *\n * `WalletAccount` properties must be read-only. This class enforces this by making all properties\n * [truly private](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Classes/Private_class_fields) and\n * read-only, using getters for access, returning copies instead of references, and calling\n * [Object.freeze](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/freeze)\n * on the instance.\n *\n * @group Account\n */\nexport class ReadonlyWalletAccount {\n  /** Implementation of {@link \"@wallet-standard/base\".WalletAccount.address | WalletAccount::address} */\n  get address() {\n    return __classPrivateFieldGet(this, _ReadonlyWalletAccount_address, \"f\");\n  }\n  /** Implementation of {@link \"@wallet-standard/base\".WalletAccount.publicKey | WalletAccount::publicKey} */\n  get publicKey() {\n    return __classPrivateFieldGet(this, _ReadonlyWalletAccount_publicKey, \"f\").slice();\n  }\n  /** Implementation of {@link \"@wallet-standard/base\".WalletAccount.chains | WalletAccount::chains} */\n  get chains() {\n    return __classPrivateFieldGet(this, _ReadonlyWalletAccount_chains, \"f\").slice();\n  }\n  /** Implementation of {@link \"@wallet-standard/base\".WalletAccount.features | WalletAccount::features} */\n  get features() {\n    return __classPrivateFieldGet(this, _ReadonlyWalletAccount_features, \"f\").slice();\n  }\n  /** Implementation of {@link \"@wallet-standard/base\".WalletAccount.label | WalletAccount::label} */\n  get label() {\n    return __classPrivateFieldGet(this, _ReadonlyWalletAccount_label, \"f\");\n  }\n  /** Implementation of {@link \"@wallet-standard/base\".WalletAccount.icon | WalletAccount::icon} */\n  get icon() {\n    return __classPrivateFieldGet(this, _ReadonlyWalletAccount_icon, \"f\");\n  }\n  /**\n   * Create and freeze a read-only account.\n   *\n   * @param account Account to copy properties from.\n   */\n  constructor(account) {\n    _ReadonlyWalletAccount_address.set(this, void 0);\n    _ReadonlyWalletAccount_publicKey.set(this, void 0);\n    _ReadonlyWalletAccount_chains.set(this, void 0);\n    _ReadonlyWalletAccount_features.set(this, void 0);\n    _ReadonlyWalletAccount_label.set(this, void 0);\n    _ReadonlyWalletAccount_icon.set(this, void 0);\n    if (new.target === ReadonlyWalletAccount) {\n      Object.freeze(this);\n    }\n    __classPrivateFieldSet(this, _ReadonlyWalletAccount_address, account.address, \"f\");\n    __classPrivateFieldSet(this, _ReadonlyWalletAccount_publicKey, account.publicKey.slice(), \"f\");\n    __classPrivateFieldSet(this, _ReadonlyWalletAccount_chains, account.chains.slice(), \"f\");\n    __classPrivateFieldSet(this, _ReadonlyWalletAccount_features, account.features.slice(), \"f\");\n    __classPrivateFieldSet(this, _ReadonlyWalletAccount_label, account.label, \"f\");\n    __classPrivateFieldSet(this, _ReadonlyWalletAccount_icon, account.icon, \"f\");\n  }\n}\n_ReadonlyWalletAccount_address = new WeakMap(), _ReadonlyWalletAccount_publicKey = new WeakMap(), _ReadonlyWalletAccount_chains = new WeakMap(), _ReadonlyWalletAccount_features = new WeakMap(), _ReadonlyWalletAccount_label = new WeakMap(), _ReadonlyWalletAccount_icon = new WeakMap();\n/**\n * Efficiently compare {@link Indexed} arrays (e.g. `Array` and `Uint8Array`).\n *\n * @param a An array.\n * @param b Another array.\n *\n * @return `true` if the arrays have the same length and elements, `false` otherwise.\n *\n * @group Util\n */\nexport function arraysEqual(a, b) {\n  if (a === b) return true;\n  const length = a.length;\n  if (length !== b.length) return false;\n  for (let i = 0; i < length; i++) {\n    if (a[i] !== b[i]) return false;\n  }\n  return true;\n}\n/**\n * Efficiently compare byte arrays, using {@link arraysEqual}.\n *\n * @param a A byte array.\n * @param b Another byte array.\n *\n * @return `true` if the byte arrays have the same length and bytes, `false` otherwise.\n *\n * @group Util\n */\nexport function bytesEqual(a, b) {\n  return arraysEqual(a, b);\n}\n/**\n * Efficiently concatenate byte arrays without modifying them.\n *\n * @param first  A byte array.\n * @param others Additional byte arrays.\n *\n * @return New byte array containing the concatenation of all the byte arrays.\n *\n * @group Util\n */\nexport function concatBytes(first, ...others) {\n  const length = others.reduce((length, bytes) => length + bytes.length, first.length);\n  const bytes = new Uint8Array(length);\n  bytes.set(first, 0);\n  for (const other of others) {\n    bytes.set(other, bytes.length);\n  }\n  return bytes;\n}\n/**\n * Create a new object with a subset of fields from a source object.\n *\n * @param source Object to pick fields from.\n * @param keys   Names of fields to pick.\n *\n * @return New object with only the picked fields.\n *\n * @group Util\n */\nexport function pick(source, ...keys) {\n  const picked = {};\n  for (const key of keys) {\n    picked[key] = source[key];\n  }\n  return picked;\n}\n/**\n * Call a callback function, catch an error if it throws, and log the error without rethrowing.\n *\n * @param callback Function to call.\n *\n * @group Util\n */\nexport function guard(callback) {\n  try {\n    callback();\n  } catch (error) {\n    console.error(error);\n  }\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "address", "__classPrivateFieldGet", "_Readonly<PERSON>alletAccount_address", "public<PERSON>ey", "_ReadonlyWalletAccount_publicKey", "slice", "chains", "_ReadonlyWalletAccount_chains", "features", "_ReadonlyWalletAccount_features", "label", "_ReadonlyWalletAccount_label", "icon", "_Readonly<PERSON>alletAccount_icon", "constructor", "account", "set", "new", "target", "Object", "freeze", "__classPrivateFieldSet", "arraysEqual", "a", "b", "length", "i", "bytesEqual", "concatBytes", "first", "others", "reduce", "bytes", "Uint8Array", "other", "pick", "source", "keys", "picked", "key", "guard", "callback", "error", "console"], "sources": ["C:\\Users\\<USER>\\Desktop\\Aptos\\freelancer-escrow-frontend\\node_modules\\@wallet-standard\\wallet\\src\\util.ts"], "sourcesContent": ["import type { ReadonlyUint8Array, WalletAccount } from '@wallet-standard/base';\n\n/**\n * Base implementation of a {@link \"@wallet-standard/base\".WalletAccount} to be used or extended by a\n * {@link \"@wallet-standard/base\".Wallet}.\n *\n * `WalletAccount` properties must be read-only. This class enforces this by making all properties\n * [truly private](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Classes/Private_class_fields) and\n * read-only, using getters for access, returning copies instead of references, and calling\n * [Object.freeze](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/freeze)\n * on the instance.\n *\n * @group Account\n */\nexport class ReadonlyWalletAccount implements WalletAccount {\n    readonly #address: WalletAccount['address'];\n    readonly #publicKey: WalletAccount['publicKey'];\n    readonly #chains: WalletAccount['chains'];\n    readonly #features: WalletAccount['features'];\n    readonly #label: WalletAccount['label'];\n    readonly #icon: WalletAccount['icon'];\n\n    /** Implementation of {@link \"@wallet-standard/base\".WalletAccount.address | WalletAccount::address} */\n    get address() {\n        return this.#address;\n    }\n\n    /** Implementation of {@link \"@wallet-standard/base\".WalletAccount.publicKey | WalletAccount::publicKey} */\n    get publicKey() {\n        return this.#publicKey.slice();\n    }\n\n    /** Implementation of {@link \"@wallet-standard/base\".WalletAccount.chains | WalletAccount::chains} */\n    get chains() {\n        return this.#chains.slice();\n    }\n\n    /** Implementation of {@link \"@wallet-standard/base\".WalletAccount.features | WalletAccount::features} */\n    get features() {\n        return this.#features.slice();\n    }\n\n    /** Implementation of {@link \"@wallet-standard/base\".WalletAccount.label | WalletAccount::label} */\n    get label() {\n        return this.#label;\n    }\n\n    /** Implementation of {@link \"@wallet-standard/base\".WalletAccount.icon | WalletAccount::icon} */\n    get icon() {\n        return this.#icon;\n    }\n\n    /**\n     * Create and freeze a read-only account.\n     *\n     * @param account Account to copy properties from.\n     */\n    constructor(account: WalletAccount) {\n        if (new.target === ReadonlyWalletAccount) {\n            Object.freeze(this);\n        }\n\n        this.#address = account.address;\n        this.#publicKey = account.publicKey.slice();\n        this.#chains = account.chains.slice();\n        this.#features = account.features.slice();\n        this.#label = account.label;\n        this.#icon = account.icon;\n    }\n}\n\n/**\n * Efficiently compare {@link Indexed} arrays (e.g. `Array` and `Uint8Array`).\n *\n * @param a An array.\n * @param b Another array.\n *\n * @return `true` if the arrays have the same length and elements, `false` otherwise.\n *\n * @group Util\n */\nexport function arraysEqual<T>(a: Indexed<T>, b: Indexed<T>): boolean {\n    if (a === b) return true;\n\n    const length = a.length;\n    if (length !== b.length) return false;\n\n    for (let i = 0; i < length; i++) {\n        if (a[i] !== b[i]) return false;\n    }\n\n    return true;\n}\n\n/**\n * Efficiently compare byte arrays, using {@link arraysEqual}.\n *\n * @param a A byte array.\n * @param b Another byte array.\n *\n * @return `true` if the byte arrays have the same length and bytes, `false` otherwise.\n *\n * @group Util\n */\nexport function bytesEqual(a: ReadonlyUint8Array, b: ReadonlyUint8Array): boolean {\n    return arraysEqual(a, b);\n}\n\n/**\n * Efficiently concatenate byte arrays without modifying them.\n *\n * @param first  A byte array.\n * @param others Additional byte arrays.\n *\n * @return New byte array containing the concatenation of all the byte arrays.\n *\n * @group Util\n */\nexport function concatBytes(first: ReadonlyUint8Array, ...others: ReadonlyUint8Array[]): Uint8Array {\n    const length = others.reduce((length, bytes) => length + bytes.length, first.length);\n    const bytes = new Uint8Array(length);\n\n    bytes.set(first, 0);\n    for (const other of others) {\n        bytes.set(other, bytes.length);\n    }\n\n    return bytes;\n}\n\n/**\n * Create a new object with a subset of fields from a source object.\n *\n * @param source Object to pick fields from.\n * @param keys   Names of fields to pick.\n *\n * @return New object with only the picked fields.\n *\n * @group Util\n */\nexport function pick<T, K extends keyof T>(source: T, ...keys: K[]): Pick<T, K> {\n    const picked = {} as Pick<T, K>;\n    for (const key of keys) {\n        picked[key] = source[key];\n    }\n    return picked;\n}\n\n/**\n * Call a callback function, catch an error if it throws, and log the error without rethrowing.\n *\n * @param callback Function to call.\n *\n * @group Util\n */\nexport function guard(callback: () => void) {\n    try {\n        callback();\n    } catch (error) {\n        console.error(error);\n    }\n}\n\n/**\n * @internal\n *\n * Type with a numeric `length` and numerically indexed elements of a generic type `T`.\n *\n * For example, `Array<T>` and `Uint8Array`.\n *\n * @group Internal\n */\nexport interface Indexed<T> {\n    length: number;\n    [index: number]: T;\n}\n"], "mappings": ";;;;;;;;;;;;AAEA;;;;;;;;;;;;AAYA,OAAM,MAAOA,qBAAqB;EAQ9B;EACA,IAAIC,OAAOA,CAAA;IACP,OAAOC,sBAAA,KAAI,EAAAC,8BAAA,MAAS;EACxB;EAEA;EACA,IAAIC,SAASA,CAAA;IACT,OAAOF,sBAAA,KAAI,EAAAG,gCAAA,MAAW,CAACC,KAAK,EAAE;EAClC;EAEA;EACA,IAAIC,MAAMA,CAAA;IACN,OAAOL,sBAAA,KAAI,EAAAM,6BAAA,MAAQ,CAACF,KAAK,EAAE;EAC/B;EAEA;EACA,IAAIG,QAAQA,CAAA;IACR,OAAOP,sBAAA,KAAI,EAAAQ,+BAAA,MAAU,CAACJ,KAAK,EAAE;EACjC;EAEA;EACA,IAAIK,KAAKA,CAAA;IACL,OAAOT,sBAAA,KAAI,EAAAU,4BAAA,MAAO;EACtB;EAEA;EACA,IAAIC,IAAIA,CAAA;IACJ,OAAOX,sBAAA,KAAI,EAAAY,2BAAA,MAAM;EACrB;EAEA;;;;;EAKAC,YAAYC,OAAsB;IA1CzBb,8BAAA,CAAAc,GAAA;IACAZ,gCAAA,CAAAY,GAAA;IACAT,6BAAA,CAAAS,GAAA;IACAP,+BAAA,CAAAO,GAAA;IACAL,4BAAA,CAAAK,GAAA;IACAH,2BAAA,CAAAG,GAAA;IAsCL,IAAIC,GAAG,CAACC,MAAM,KAAKnB,qBAAqB,EAAE;MACtCoB,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IACvB;IAEAC,sBAAA,KAAI,EAAAnB,8BAAA,EAAYa,OAAO,CAACf,OAAO;IAC/BqB,sBAAA,KAAI,EAAAjB,gCAAA,EAAcW,OAAO,CAACZ,SAAS,CAACE,KAAK,EAAE;IAC3CgB,sBAAA,KAAI,EAAAd,6BAAA,EAAWQ,OAAO,CAACT,MAAM,CAACD,KAAK,EAAE;IACrCgB,sBAAA,KAAI,EAAAZ,+BAAA,EAAaM,OAAO,CAACP,QAAQ,CAACH,KAAK,EAAE;IACzCgB,sBAAA,KAAI,EAAAV,4BAAA,EAAUI,OAAO,CAACL,KAAK;IAC3BW,sBAAA,KAAI,EAAAR,2BAAA,EAASE,OAAO,CAACH,IAAI;EAC7B;;;AAGJ;;;;;;;;;;AAUA,OAAM,SAAUU,WAAWA,CAAIC,CAAa,EAAEC,CAAa;EACvD,IAAID,CAAC,KAAKC,CAAC,EAAE,OAAO,IAAI;EAExB,MAAMC,MAAM,GAAGF,CAAC,CAACE,MAAM;EACvB,IAAIA,MAAM,KAAKD,CAAC,CAACC,MAAM,EAAE,OAAO,KAAK;EAErC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,MAAM,EAAEC,CAAC,EAAE,EAAE;IAC7B,IAAIH,CAAC,CAACG,CAAC,CAAC,KAAKF,CAAC,CAACE,CAAC,CAAC,EAAE,OAAO,KAAK;EACnC;EAEA,OAAO,IAAI;AACf;AAEA;;;;;;;;;;AAUA,OAAM,SAAUC,UAAUA,CAACJ,CAAqB,EAAEC,CAAqB;EACnE,OAAOF,WAAW,CAACC,CAAC,EAAEC,CAAC,CAAC;AAC5B;AAEA;;;;;;;;;;AAUA,OAAM,SAAUI,WAAWA,CAACC,KAAyB,EAAE,GAAGC,MAA4B;EAClF,MAAML,MAAM,GAAGK,MAAM,CAACC,MAAM,CAAC,CAACN,MAAM,EAAEO,KAAK,KAAKP,MAAM,GAAGO,KAAK,CAACP,MAAM,EAAEI,KAAK,CAACJ,MAAM,CAAC;EACpF,MAAMO,KAAK,GAAG,IAAIC,UAAU,CAACR,MAAM,CAAC;EAEpCO,KAAK,CAAChB,GAAG,CAACa,KAAK,EAAE,CAAC,CAAC;EACnB,KAAK,MAAMK,KAAK,IAAIJ,MAAM,EAAE;IACxBE,KAAK,CAAChB,GAAG,CAACkB,KAAK,EAAEF,KAAK,CAACP,MAAM,CAAC;EAClC;EAEA,OAAOO,KAAK;AAChB;AAEA;;;;;;;;;;AAUA,OAAM,SAAUG,IAAIA,CAAuBC,MAAS,EAAE,GAAGC,IAAS;EAC9D,MAAMC,MAAM,GAAG,EAAgB;EAC/B,KAAK,MAAMC,GAAG,IAAIF,IAAI,EAAE;IACpBC,MAAM,CAACC,GAAG,CAAC,GAAGH,MAAM,CAACG,GAAG,CAAC;EAC7B;EACA,OAAOD,MAAM;AACjB;AAEA;;;;;;;AAOA,OAAM,SAAUE,KAAKA,CAACC,QAAoB;EACtC,IAAI;IACAA,QAAQ,EAAE;EACd,CAAC,CAAC,OAAOC,KAAK,EAAE;IACZC,OAAO,CAACD,KAAK,CAACA,KAAK,CAAC;EACxB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}