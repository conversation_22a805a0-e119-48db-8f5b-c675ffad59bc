{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"prefixCls\", \"invalidate\", \"item\", \"renderItem\", \"responsive\", \"responsiveDisabled\", \"registerSize\", \"itemKey\", \"className\", \"style\", \"children\", \"display\", \"order\", \"component\"];\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport ResizeObserver from 'rc-resize-observer';\n// Use shared variable to save bundle size\nvar UNDEFINED = undefined;\nfunction InternalItem(props, ref) {\n  var prefixCls = props.prefixCls,\n    invalidate = props.invalidate,\n    item = props.item,\n    renderItem = props.renderItem,\n    responsive = props.responsive,\n    responsiveDisabled = props.responsiveDisabled,\n    registerSize = props.registerSize,\n    itemKey = props.itemKey,\n    className = props.className,\n    style = props.style,\n    children = props.children,\n    display = props.display,\n    order = props.order,\n    _props$component = props.component,\n    Component = _props$component === void 0 ? 'div' : _props$component,\n    restProps = _objectWithoutProperties(props, _excluded);\n  var mergedHidden = responsive && !display;\n\n  // ================================ Effect ================================\n  function internalRegisterSize(width) {\n    registerSize(itemKey, width);\n  }\n  React.useEffect(function () {\n    return function () {\n      internalRegisterSize(null);\n    };\n  }, []);\n\n  // ================================ Render ================================\n  var childNode = renderItem && item !== UNDEFINED ? renderItem(item, {\n    index: order\n  }) : children;\n  var overflowStyle;\n  if (!invalidate) {\n    overflowStyle = {\n      opacity: mergedHidden ? 0 : 1,\n      height: mergedHidden ? 0 : UNDEFINED,\n      overflowY: mergedHidden ? 'hidden' : UNDEFINED,\n      order: responsive ? order : UNDEFINED,\n      pointerEvents: mergedHidden ? 'none' : UNDEFINED,\n      position: mergedHidden ? 'absolute' : UNDEFINED\n    };\n  }\n  var overflowProps = {};\n  if (mergedHidden) {\n    overflowProps['aria-hidden'] = true;\n  }\n  var itemNode = /*#__PURE__*/React.createElement(Component, _extends({\n    className: classNames(!invalidate && prefixCls, className),\n    style: _objectSpread(_objectSpread({}, overflowStyle), style)\n  }, overflowProps, restProps, {\n    ref: ref\n  }), childNode);\n  if (responsive) {\n    itemNode = /*#__PURE__*/React.createElement(ResizeObserver, {\n      onResize: function onResize(_ref) {\n        var offsetWidth = _ref.offsetWidth;\n        internalRegisterSize(offsetWidth);\n      },\n      disabled: responsiveDisabled\n    }, itemNode);\n  }\n  return itemNode;\n}\nvar Item = /*#__PURE__*/React.forwardRef(InternalItem);\nItem.displayName = 'Item';\nexport default Item;", "map": {"version": 3, "names": ["_extends", "_objectSpread", "_objectWithoutProperties", "_excluded", "React", "classNames", "ResizeObserver", "UNDEFINED", "undefined", "InternalItem", "props", "ref", "prefixCls", "invalidate", "item", "renderItem", "responsive", "responsiveDisabled", "registerSize", "itemKey", "className", "style", "children", "display", "order", "_props$component", "component", "Component", "restProps", "mergedHidden", "internalRegisterSize", "width", "useEffect", "childNode", "index", "overflowStyle", "opacity", "height", "overflowY", "pointerEvents", "position", "overflowProps", "itemNode", "createElement", "onResize", "_ref", "offsetWidth", "disabled", "<PERSON><PERSON>", "forwardRef", "displayName"], "sources": ["C:/Users/<USER>/Desktop/Aptos/freelancer-escrow-frontend/node_modules/rc-overflow/es/Item.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"prefixCls\", \"invalidate\", \"item\", \"renderItem\", \"responsive\", \"responsiveDisabled\", \"registerSize\", \"itemKey\", \"className\", \"style\", \"children\", \"display\", \"order\", \"component\"];\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport ResizeObserver from 'rc-resize-observer';\n// Use shared variable to save bundle size\nvar UNDEFINED = undefined;\nfunction InternalItem(props, ref) {\n  var prefixCls = props.prefixCls,\n    invalidate = props.invalidate,\n    item = props.item,\n    renderItem = props.renderItem,\n    responsive = props.responsive,\n    responsiveDisabled = props.responsiveDisabled,\n    registerSize = props.registerSize,\n    itemKey = props.itemKey,\n    className = props.className,\n    style = props.style,\n    children = props.children,\n    display = props.display,\n    order = props.order,\n    _props$component = props.component,\n    Component = _props$component === void 0 ? 'div' : _props$component,\n    restProps = _objectWithoutProperties(props, _excluded);\n  var mergedHidden = responsive && !display;\n\n  // ================================ Effect ================================\n  function internalRegisterSize(width) {\n    registerSize(itemKey, width);\n  }\n  React.useEffect(function () {\n    return function () {\n      internalRegisterSize(null);\n    };\n  }, []);\n\n  // ================================ Render ================================\n  var childNode = renderItem && item !== UNDEFINED ? renderItem(item, {\n    index: order\n  }) : children;\n  var overflowStyle;\n  if (!invalidate) {\n    overflowStyle = {\n      opacity: mergedHidden ? 0 : 1,\n      height: mergedHidden ? 0 : UNDEFINED,\n      overflowY: mergedHidden ? 'hidden' : UNDEFINED,\n      order: responsive ? order : UNDEFINED,\n      pointerEvents: mergedHidden ? 'none' : UNDEFINED,\n      position: mergedHidden ? 'absolute' : UNDEFINED\n    };\n  }\n  var overflowProps = {};\n  if (mergedHidden) {\n    overflowProps['aria-hidden'] = true;\n  }\n  var itemNode = /*#__PURE__*/React.createElement(Component, _extends({\n    className: classNames(!invalidate && prefixCls, className),\n    style: _objectSpread(_objectSpread({}, overflowStyle), style)\n  }, overflowProps, restProps, {\n    ref: ref\n  }), childNode);\n  if (responsive) {\n    itemNode = /*#__PURE__*/React.createElement(ResizeObserver, {\n      onResize: function onResize(_ref) {\n        var offsetWidth = _ref.offsetWidth;\n        internalRegisterSize(offsetWidth);\n      },\n      disabled: responsiveDisabled\n    }, itemNode);\n  }\n  return itemNode;\n}\nvar Item = /*#__PURE__*/React.forwardRef(InternalItem);\nItem.displayName = 'Item';\nexport default Item;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,IAAIC,SAAS,GAAG,CAAC,WAAW,EAAE,YAAY,EAAE,MAAM,EAAE,YAAY,EAAE,YAAY,EAAE,oBAAoB,EAAE,cAAc,EAAE,SAAS,EAAE,WAAW,EAAE,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE,OAAO,EAAE,WAAW,CAAC;AACnM,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,cAAc,MAAM,oBAAoB;AAC/C;AACA,IAAIC,SAAS,GAAGC,SAAS;AACzB,SAASC,YAAYA,CAACC,KAAK,EAAEC,GAAG,EAAE;EAChC,IAAIC,SAAS,GAAGF,KAAK,CAACE,SAAS;IAC7BC,UAAU,GAAGH,KAAK,CAACG,UAAU;IAC7BC,IAAI,GAAGJ,KAAK,CAACI,IAAI;IACjBC,UAAU,GAAGL,KAAK,CAACK,UAAU;IAC7BC,UAAU,GAAGN,KAAK,CAACM,UAAU;IAC7BC,kBAAkB,GAAGP,KAAK,CAACO,kBAAkB;IAC7CC,YAAY,GAAGR,KAAK,CAACQ,YAAY;IACjCC,OAAO,GAAGT,KAAK,CAACS,OAAO;IACvBC,SAAS,GAAGV,KAAK,CAACU,SAAS;IAC3BC,KAAK,GAAGX,KAAK,CAACW,KAAK;IACnBC,QAAQ,GAAGZ,KAAK,CAACY,QAAQ;IACzBC,OAAO,GAAGb,KAAK,CAACa,OAAO;IACvBC,KAAK,GAAGd,KAAK,CAACc,KAAK;IACnBC,gBAAgB,GAAGf,KAAK,CAACgB,SAAS;IAClCC,SAAS,GAAGF,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,gBAAgB;IAClEG,SAAS,GAAG1B,wBAAwB,CAACQ,KAAK,EAAEP,SAAS,CAAC;EACxD,IAAI0B,YAAY,GAAGb,UAAU,IAAI,CAACO,OAAO;;EAEzC;EACA,SAASO,oBAAoBA,CAACC,KAAK,EAAE;IACnCb,YAAY,CAACC,OAAO,EAAEY,KAAK,CAAC;EAC9B;EACA3B,KAAK,CAAC4B,SAAS,CAAC,YAAY;IAC1B,OAAO,YAAY;MACjBF,oBAAoB,CAAC,IAAI,CAAC;IAC5B,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,IAAIG,SAAS,GAAGlB,UAAU,IAAID,IAAI,KAAKP,SAAS,GAAGQ,UAAU,CAACD,IAAI,EAAE;IAClEoB,KAAK,EAAEV;EACT,CAAC,CAAC,GAAGF,QAAQ;EACb,IAAIa,aAAa;EACjB,IAAI,CAACtB,UAAU,EAAE;IACfsB,aAAa,GAAG;MACdC,OAAO,EAAEP,YAAY,GAAG,CAAC,GAAG,CAAC;MAC7BQ,MAAM,EAAER,YAAY,GAAG,CAAC,GAAGtB,SAAS;MACpC+B,SAAS,EAAET,YAAY,GAAG,QAAQ,GAAGtB,SAAS;MAC9CiB,KAAK,EAAER,UAAU,GAAGQ,KAAK,GAAGjB,SAAS;MACrCgC,aAAa,EAAEV,YAAY,GAAG,MAAM,GAAGtB,SAAS;MAChDiC,QAAQ,EAAEX,YAAY,GAAG,UAAU,GAAGtB;IACxC,CAAC;EACH;EACA,IAAIkC,aAAa,GAAG,CAAC,CAAC;EACtB,IAAIZ,YAAY,EAAE;IAChBY,aAAa,CAAC,aAAa,CAAC,GAAG,IAAI;EACrC;EACA,IAAIC,QAAQ,GAAG,aAAatC,KAAK,CAACuC,aAAa,CAAChB,SAAS,EAAE3B,QAAQ,CAAC;IAClEoB,SAAS,EAAEf,UAAU,CAAC,CAACQ,UAAU,IAAID,SAAS,EAAEQ,SAAS,CAAC;IAC1DC,KAAK,EAAEpB,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEkC,aAAa,CAAC,EAAEd,KAAK;EAC9D,CAAC,EAAEoB,aAAa,EAAEb,SAAS,EAAE;IAC3BjB,GAAG,EAAEA;EACP,CAAC,CAAC,EAAEsB,SAAS,CAAC;EACd,IAAIjB,UAAU,EAAE;IACd0B,QAAQ,GAAG,aAAatC,KAAK,CAACuC,aAAa,CAACrC,cAAc,EAAE;MAC1DsC,QAAQ,EAAE,SAASA,QAAQA,CAACC,IAAI,EAAE;QAChC,IAAIC,WAAW,GAAGD,IAAI,CAACC,WAAW;QAClChB,oBAAoB,CAACgB,WAAW,CAAC;MACnC,CAAC;MACDC,QAAQ,EAAE9B;IACZ,CAAC,EAAEyB,QAAQ,CAAC;EACd;EACA,OAAOA,QAAQ;AACjB;AACA,IAAIM,IAAI,GAAG,aAAa5C,KAAK,CAAC6C,UAAU,CAACxC,YAAY,CAAC;AACtDuC,IAAI,CAACE,WAAW,GAAG,MAAM;AACzB,eAAeF,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}