{"ast": null, "code": "import * as React from 'react';\nimport ResizeObserver from 'rc-resize-observer';\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nexport default function MeasureCell(_ref) {\n  var columnKey = _ref.columnKey,\n    onColumnResize = _ref.onColumnResize;\n  var cellRef = React.useRef();\n  useLayoutEffect(function () {\n    if (cellRef.current) {\n      onColumnResize(columnKey, cellRef.current.offsetWidth);\n    }\n  }, []);\n  return /*#__PURE__*/React.createElement(ResizeObserver, {\n    data: columnKey\n  }, /*#__PURE__*/React.createElement(\"td\", {\n    ref: cellRef,\n    style: {\n      padding: 0,\n      border: 0,\n      height: 0\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      height: 0,\n      overflow: 'hidden'\n    }\n  }, \"\\xA0\")));\n}", "map": {"version": 3, "names": ["React", "ResizeObserver", "useLayoutEffect", "MeasureCell", "_ref", "column<PERSON>ey", "onColumnResize", "cellRef", "useRef", "current", "offsetWidth", "createElement", "data", "ref", "style", "padding", "border", "height", "overflow"], "sources": ["C:/Users/<USER>/Desktop/Aptos/freelancer-escrow-frontend/node_modules/rc-table/es/Body/MeasureCell.js"], "sourcesContent": ["import * as React from 'react';\nimport ResizeObserver from 'rc-resize-observer';\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nexport default function MeasureCell(_ref) {\n  var columnKey = _ref.columnKey,\n    onColumnResize = _ref.onColumnResize;\n  var cellRef = React.useRef();\n  useLayoutEffect(function () {\n    if (cellRef.current) {\n      onColumnResize(columnKey, cellRef.current.offsetWidth);\n    }\n  }, []);\n  return /*#__PURE__*/React.createElement(ResizeObserver, {\n    data: columnKey\n  }, /*#__PURE__*/React.createElement(\"td\", {\n    ref: cellRef,\n    style: {\n      padding: 0,\n      border: 0,\n      height: 0\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      height: 0,\n      overflow: 'hidden'\n    }\n  }, \"\\xA0\")));\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,cAAc,MAAM,oBAAoB;AAC/C,OAAOC,eAAe,MAAM,kCAAkC;AAC9D,eAAe,SAASC,WAAWA,CAACC,IAAI,EAAE;EACxC,IAAIC,SAAS,GAAGD,IAAI,CAACC,SAAS;IAC5BC,cAAc,GAAGF,IAAI,CAACE,cAAc;EACtC,IAAIC,OAAO,GAAGP,KAAK,CAACQ,MAAM,CAAC,CAAC;EAC5BN,eAAe,CAAC,YAAY;IAC1B,IAAIK,OAAO,CAACE,OAAO,EAAE;MACnBH,cAAc,CAACD,SAAS,EAAEE,OAAO,CAACE,OAAO,CAACC,WAAW,CAAC;IACxD;EACF,CAAC,EAAE,EAAE,CAAC;EACN,OAAO,aAAaV,KAAK,CAACW,aAAa,CAACV,cAAc,EAAE;IACtDW,IAAI,EAAEP;EACR,CAAC,EAAE,aAAaL,KAAK,CAACW,aAAa,CAAC,IAAI,EAAE;IACxCE,GAAG,EAAEN,OAAO;IACZO,KAAK,EAAE;MACLC,OAAO,EAAE,CAAC;MACVC,MAAM,EAAE,CAAC;MACTC,MAAM,EAAE;IACV;EACF,CAAC,EAAE,aAAajB,KAAK,CAACW,aAAa,CAAC,KAAK,EAAE;IACzCG,KAAK,EAAE;MACLG,MAAM,EAAE,CAAC;MACTC,QAAQ,EAAE;IACZ;EACF,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}