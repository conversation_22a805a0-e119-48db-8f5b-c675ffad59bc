import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import BehanceOutlinedSvg from "@ant-design/icons-svg/es/asn/BehanceOutlined";
import AntdIcon from "../components/AntdIcon";
var BehanceOutlined = function BehanceOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: BehanceOutlinedSvg
  }));
};

/**![behance](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTYzNCAyOTQuM2gxOTkuNXY0OC40SDYzNHpNNDM0LjEgNDg1LjhjNDQuMS0yMS4xIDY3LjItNTMuMiA2Ny4yLTEwMi44IDAtOTguMS03My0xMjEuOS0xNTcuMy0xMjEuOUgxMTJ2NDkyLjRoMjM4LjVjODkuNCAwIDE3My4zLTQzIDE3My4zLTE0MyAwLTYxLjgtMjkuMi0xMDcuNS04OS43LTEyNC43ek0yMjAuMiAzNDUuMWgxMDEuNWMzOS4xIDAgNzQuMiAxMC45IDc0LjIgNTYuMyAwIDQxLjgtMjcuMyA1OC42LTY2IDU4LjZIMjIwLjJWMzQ1LjF6bTExNS41IDMyNC44SDIyMC4xVjUzNC4zSDMzOGM0Ny42IDAgNzcuNyAxOS45IDc3LjcgNzAuMyAwIDQ5LjYtMzUuOSA2NS4zLTgwIDY1LjN6bTU3NS44LTg5LjVjMC0xMDUuNS02MS43LTE5My40LTE3My4zLTE5My40LTEwOC41IDAtMTgyLjMgODEuNy0xODIuMyAxODguOCAwIDExMSA2OS45IDE4Ny4yIDE4Mi4zIDE4Ny4yIDg1LjEgMCAxNDAuMi0zOC4zIDE2Ni43LTEyMGgtODYuM2MtOS40IDMwLjUtNDcuNiA0Ni41LTc3LjMgNDYuNS01Ny40IDAtODcuNC0zMy42LTg3LjQtOTAuN2gyNTYuOWMuMy01LjkuNy0xMi4xLjctMTguNHpNNjUzLjkgNTM3YzMuMS00Ni45IDM0LjQtNzYuMiA4MS4yLTc2LjIgNDkuMiAwIDczLjggMjguOSA3OC4xIDc2LjJINjUzLjl6IiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(BehanceOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'BehanceOutlined';
}
export default RefIcon;