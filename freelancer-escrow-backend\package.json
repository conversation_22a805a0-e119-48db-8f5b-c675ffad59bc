{"name": "freelancer-escrow-backend", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "setup-db": "node setup-mongodb.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@aptos-labs/ts-sdk": "^4.0.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.18.0"}, "devDependencies": {"@types/node": "^24.3.0", "nodemon": "^3.1.10", "typescript": "^5.9.2"}}