module project::message {
    use std::string;
    use std::signer;
    
    struct MessageHolder has key {
        message: string::String,
    }
    
    /// Initialize or update message holder
    public entry fun set_message(account: &signer, message: string::String) acquires MessageHolder {
        let account_addr = signer::address_of(account);
        if (!exists<MessageHolder>(account_addr)) {
            move_to(account, MessageHolder {
                message,
            });
        } else {
            let message_holder = borrow_global_mut<MessageHolder>(account_addr);
            message_holder.message = message;
        }
    }
    
    #[view]
    /// Get message from account
    public fun get_message(account_addr: address): string::String acquires MessageHolder {
        if (exists<MessageHolder>(account_addr)) {
            borrow_global<MessageHolder>(account_addr).message
        } else {
            string::utf8(b"No message found")
        }
    }
}