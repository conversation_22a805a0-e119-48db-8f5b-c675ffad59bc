#[test_only]
module freelancer_escrow::security_tests {
    use std::signer;
    use std::string;
    use std::vector;
    use aptos_framework::coin;
    use aptos_framework::aptos_coin::{Self, AptosCoin};
    use aptos_framework::timestamp;
    use aptos_framework::account;
    use freelancer_escrow::escrow;
    use freelancer_escrow::security;

    // Test accounts
    const ADMIN: address = @0x100;
    const SECURITY_ADMIN: address = @0x101;
    const CLIENT: address = @0x200;
    const FREELANCER: address = @0x300;
    const REGISTRY: address = @0x400;
    const MALICIOUS_USER: address = @0x500;

    #[test(aptos_framework = @0x1, security_admin = @0x101)]
    public fun test_security_initialization(
        aptos_framework: &signer,
        security_admin: &signer,
    ) {
        timestamp::set_time_has_started_for_testing(aptos_framework);
        account::create_account_for_test(signer::address_of(security_admin));
        
        // Initialize security system
        security::initialize_security(security_admin);
        
        // Verify configuration
        let (max_escrows_per_hour, min_amount, max_amount, dispute_timeout) = 
            security::get_security_config(signer::address_of(security_admin));
        
        assert!(max_escrows_per_hour == 10, 1);
        assert!(min_amount == 1000000, 2); // 0.01 APT
        assert!(max_amount == *************, 3); // 10,000 APT
        assert!(dispute_timeout == 168, 4); // 7 days
    }

    #[test(aptos_framework = @0x1, security_admin = @0x101, malicious = @0x500)]
    public fun test_blacklist_functionality(
        aptos_framework: &signer,
        security_admin: &signer,
        malicious: &signer,
    ) {
        timestamp::set_time_has_started_for_testing(aptos_framework);
        account::create_account_for_test(signer::address_of(security_admin));
        account::create_account_for_test(signer::address_of(malicious));
        
        // Initialize security system
        security::initialize_security(security_admin);
        
        let malicious_addr = signer::address_of(malicious);
        
        // Initially not blacklisted
        assert!(!security::is_blacklisted(malicious_addr, signer::address_of(security_admin)), 5);
        
        // Blacklist the address
        security::blacklist_address(
            security_admin,
            malicious_addr,
            b"Suspicious activity detected",
            signer::address_of(security_admin),
        );
        
        // Verify blacklisted
        assert!(security::is_blacklisted(malicious_addr, signer::address_of(security_admin)), 6);
        
        // Check blacklisted addresses list
        let blacklisted = security::get_blacklisted_addresses(signer::address_of(security_admin));
        assert!(vector::contains(&blacklisted, &malicious_addr), 7);
        
        // Unblacklist the address
        security::unblacklist_address(
            security_admin,
            malicious_addr,
            signer::address_of(security_admin),
        );
        
        // Verify no longer blacklisted
        assert!(!security::is_blacklisted(malicious_addr, signer::address_of(security_admin)), 8);
    }

    #[test(aptos_framework = @0x1, security_admin = @0x101, client = @0x200)]
    public fun test_rate_limiting(
        aptos_framework: &signer,
        security_admin: &signer,
        client: &signer,
    ) {
        timestamp::set_time_has_started_for_testing(aptos_framework);
        account::create_account_for_test(signer::address_of(security_admin));
        account::create_account_for_test(signer::address_of(client));
        
        // Initialize security system
        security::initialize_security(security_admin);
        security::initialize_rate_limiting(client);
        
        let client_addr = signer::address_of(client);
        let security_admin_addr = signer::address_of(security_admin);
        
        // Initially should be able to create escrows
        assert!(security::check_rate_limit(client_addr, security_admin_addr), 9);
        
        // Record multiple escrow creations (up to the limit)
        let i = 0;
        while (i < 9) { // Record 9, so we have 9 total
            security::record_escrow_creation(client_addr);
            i = i + 1;
        };

        // Should still be under the limit (9 < 10)
        assert!(security::check_rate_limit(client_addr, security_admin_addr), 10);

        // Record one more to reach the limit
        security::record_escrow_creation(client_addr);

        // Now should be at the limit (10 == 10, so not under limit)
        assert!(!security::check_rate_limit(client_addr, security_admin_addr), 11);
        
        // Check rate limit status
        let (escrow_count, _last_cleanup) = security::get_user_rate_limit_status(client_addr);
        assert!(escrow_count == 10, 12);
    }

    #[test(aptos_framework = @0x1, security_admin = @0x101)]
    public fun test_amount_validation(
        aptos_framework: &signer,
        security_admin: &signer,
    ) {
        timestamp::set_time_has_started_for_testing(aptos_framework);
        account::create_account_for_test(signer::address_of(security_admin));
        
        // Initialize security system
        security::initialize_security(security_admin);
        
        let security_admin_addr = signer::address_of(security_admin);
        
        // Test valid amounts
        assert!(security::validate_amount(1000000, security_admin_addr), 13); // 0.01 APT
        assert!(security::validate_amount(*********, security_admin_addr), 14); // 1 APT
        assert!(security::validate_amount(*************, security_admin_addr), 15); // 10,000 APT
        
        // Test invalid amounts
        assert!(!security::validate_amount(999999, security_admin_addr), 16); // Too small
        assert!(!security::validate_amount(**********001, security_admin_addr), 17); // Too large
        assert!(!security::validate_amount(0, security_admin_addr), 18); // Zero
    }

    #[test(aptos_framework = @0x1, security_admin = @0x101)]
    #[expected_failure(abort_code = 1, location = freelancer_escrow::security)]
    public fun test_unauthorized_config_update(
        aptos_framework: &signer,
        security_admin: &signer,
    ) {
        timestamp::set_time_has_started_for_testing(aptos_framework);
        account::create_account_for_test(signer::address_of(security_admin));
        account::create_account_for_test(@0x999);
        
        // Initialize security system
        security::initialize_security(security_admin);
        
        // Try to update config with unauthorized account
        let unauthorized = account::create_signer_for_test(@0x999);
        security::update_config(
            &unauthorized,
            20, // max_escrows_per_hour
            2000000, // min_amount
            *************, // max_amount
            336, // dispute_timeout_hours
            signer::address_of(security_admin),
        );
    }

    #[test(aptos_framework = @0x1, admin = @0x100, security_admin = @0x101, registry = @0x400, client = @0x200, freelancer = @0x300)]
    #[expected_failure(abort_code = 10, location = freelancer_escrow::escrow)]
    public fun test_blacklisted_user_cannot_create_escrow(
        aptos_framework: &signer,
        admin: &signer,
        security_admin: &signer,
        registry: &signer,
        client: &signer,
        freelancer: &signer,
    ) {
        // Setup
        timestamp::set_time_has_started_for_testing(aptos_framework);
        let (burn_cap, mint_cap) = aptos_coin::initialize_for_test(aptos_framework);
        
        // Create accounts
        account::create_account_for_test(signer::address_of(client));
        account::create_account_for_test(signer::address_of(freelancer));
        account::create_account_for_test(signer::address_of(registry));
        account::create_account_for_test(signer::address_of(security_admin));
        
        // Initialize systems
        security::initialize_security(security_admin);
        escrow::initialize(registry, signer::address_of(security_admin));
        
        // Blacklist the client
        security::blacklist_address(
            security_admin,
            signer::address_of(client),
            b"Fraudulent activity",
            signer::address_of(security_admin),
        );
        
        // Mint coins for client
        let coins = coin::mint<AptosCoin>(**********, &mint_cap);
        coin::deposit(signer::address_of(client), coins);
        
        // Try to create escrow (should fail)
        escrow::create_escrow(
            client,
            signer::address_of(freelancer),
            **********,
            string::utf8(b"Test Project"),
            string::utf8(b"Test Description"),
            168,
            signer::address_of(registry),
        );
        
        coin::destroy_burn_cap(burn_cap);
        coin::destroy_mint_cap(mint_cap);
    }

    #[test(aptos_framework = @0x1, admin = @0x100, security_admin = @0x101, registry = @0x400, client = @0x200, freelancer = @0x300)]
    #[expected_failure(abort_code = 8, location = freelancer_escrow::escrow)]
    public fun test_invalid_amount_rejected(
        aptos_framework: &signer,
        admin: &signer,
        security_admin: &signer,
        registry: &signer,
        client: &signer,
        freelancer: &signer,
    ) {
        // Setup
        timestamp::set_time_has_started_for_testing(aptos_framework);
        let (burn_cap, mint_cap) = aptos_coin::initialize_for_test(aptos_framework);
        
        // Create accounts
        account::create_account_for_test(signer::address_of(client));
        account::create_account_for_test(signer::address_of(freelancer));
        account::create_account_for_test(signer::address_of(registry));
        account::create_account_for_test(signer::address_of(security_admin));
        
        // Initialize systems
        security::initialize_security(security_admin);
        escrow::initialize(registry, signer::address_of(security_admin));
        
        // Try to create escrow with invalid amount (too small)
        escrow::create_escrow(
            client,
            signer::address_of(freelancer),
            999999, // Below minimum
            string::utf8(b"Test Project"),
            string::utf8(b"Test Description"),
            168,
            signer::address_of(registry),
        );
        
        coin::destroy_burn_cap(burn_cap);
        coin::destroy_mint_cap(mint_cap);
    }
}
