{"ast": null, "code": "/** Name of the feature. */\nexport const StandardConnect = 'standard:connect';\n/**\n * @deprecated Use {@link StandardConnect} instead.\n *\n * @group Deprecated\n */\nexport const Connect = StandardConnect;", "map": {"version": 3, "names": ["StandardConnect", "Connect"], "sources": ["C:\\Users\\<USER>\\Desktop\\Aptos\\freelancer-escrow-frontend\\node_modules\\@wallet-standard\\features\\src\\connect.ts"], "sourcesContent": ["import type { WalletAccount } from '@wallet-standard/base';\n\n/** Name of the feature. */\nexport const StandardConnect = 'standard:connect';\n/**\n * @deprecated Use {@link StandardConnect} instead.\n *\n * @group Deprecated\n */\nexport const Connect = StandardConnect;\n\n/**\n * `standard:connect` is a {@link \"@wallet-standard/base\".Wallet.features | feature} that may be implemented by a\n * {@link \"@wallet-standard/base\".Wallet} to allow the app to obtain authorization to use\n * {@link \"@wallet-standard/base\".Wallet.accounts}.\n *\n * @group Connect\n */\nexport type StandardConnectFeature = {\n    /** Name of the feature. */\n    readonly [StandardConnect]: {\n        /** Version of the feature implemented by the Wallet. */\n        readonly version: StandardConnectVersion;\n        /** Method to call to use the feature. */\n        readonly connect: StandardConnectMethod;\n    };\n};\n/**\n * @deprecated Use {@link StandardConnectFeature} instead.\n *\n * @group Deprecated\n */\nexport type ConnectFeature = StandardConnectFeature;\n\n/**\n * Version of the {@link StandardConnectFeature} implemented by a {@link \"@wallet-standard/base\".Wallet}.\n *\n * @group Connect\n */\nexport type StandardConnectVersion = '1.0.0';\n/**\n * @deprecated Use {@link StandardConnectVersion} instead.\n *\n * @group Deprecated\n */\nexport type ConnectVersion = StandardConnectVersion;\n\n/**\n * Method to call to use the {@link StandardConnectFeature}.\n *\n * @group Connect\n */\nexport type StandardConnectMethod = (input?: StandardConnectInput) => Promise<StandardConnectOutput>;\n/**\n * @deprecated Use {@link StandardConnectMethod} instead.\n *\n * @group Deprecated\n */\nexport type ConnectMethod = StandardConnectMethod;\n\n/**\n * Input for the {@link StandardConnectMethod}.\n *\n * @group Connect\n */\nexport interface StandardConnectInput {\n    /**\n     * By default, using the {@link StandardConnectFeature} should prompt the user to request authorization to accounts.\n     * Set the `silent` flag to `true` to request accounts that have already been authorized without prompting.\n     *\n     * This flag may or may not be used by the Wallet and the app should not depend on it being used.\n     * If this flag is used by the Wallet, the Wallet should not prompt the user, and should return only the accounts\n     * that the app is authorized to use.\n     */\n    readonly silent?: boolean;\n}\n/**\n * @deprecated Use {@link StandardConnectInput} instead.\n *\n * @group Deprecated\n */\nexport type ConnectInput = StandardConnectInput;\n\n/**\n * Output of the {@link StandardConnectMethod}.\n *\n * @group Connect\n */\nexport interface StandardConnectOutput {\n    /** List of accounts in the {@link \"@wallet-standard/base\".Wallet} that the app has been authorized to use. */\n    readonly accounts: readonly WalletAccount[];\n}\n/**\n * @deprecated Use {@link StandardConnectOutput} instead.\n *\n * @group Deprecated\n */\nexport type ConnectOutput = StandardConnectOutput;\n"], "mappings": "AAEA;AACA,OAAO,MAAMA,eAAe,GAAG,kBAAkB;AACjD;;;;;AAKA,OAAO,MAAMC,OAAO,GAAGD,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}