"use strict";
"use client";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _react = _interopRequireWildcard(require("react"));
var _ActionButton = _interopRequireDefault(require("../../_util/ActionButton"));
var _context = require("../context");
const ConfirmCancelBtn = () => {
  const {
    autoFocusButton,
    cancelButtonProps,
    cancelTextLocale,
    isSilent,
    mergedOkCancel,
    rootPrefixCls,
    close,
    onCancel,
    onConfirm
  } = (0, _react.useContext)(_context.ModalContext);
  return mergedOkCancel ? (/*#__PURE__*/_react.default.createElement(_ActionButton.default, {
    isSilent: isSilent,
    actionFn: onCancel,
    close: (...args) => {
      close === null || close === void 0 ? void 0 : close.apply(void 0, args);
      onConfirm === null || onConfirm === void 0 ? void 0 : onConfirm(false);
    },
    autoFocus: autoFocusButton === 'cancel',
    buttonProps: cancelButtonProps,
    prefixCls: `${rootPrefixCls}-btn`
  }, cancelTextLocale)) : null;
};
var _default = exports.default = ConfirmCancelBtn;