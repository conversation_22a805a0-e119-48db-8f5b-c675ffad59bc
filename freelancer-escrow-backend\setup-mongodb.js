// MongoDB Setup Script for FreelanceEscrow
// Run this script to initialize your MongoDB database with sample data

const mongoose = require('mongoose');
require('dotenv').config();

// Import models
const User = require('./models/User');
const Escrow = require('./models/Escrow');

const setupDatabase = async () => {
  try {
    console.log('🔄 Connecting to MongoDB...');
    
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/freelancer-escrow', {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    
    console.log('✅ Connected to MongoDB successfully!');
    console.log('📍 Database:', mongoose.connection.name);
    console.log('🌐 Host:', mongoose.connection.host);
    console.log('🔌 Port:', mongoose.connection.port);
    
    // Clear existing data (optional - uncomment if you want to reset)
    // console.log('🧹 Clearing existing data...');
    // await User.deleteMany({});
    // await Escrow.deleteMany({});
    
    // Create sample users
    console.log('👥 Creating sample users...');
    
    const sampleUsers = [
      {
        username: 'john_client',
        email: '<EMAIL>',
        password: 'password123',
        walletAddress: '0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef12',
        role: 'client',
        profile: {
          firstName: 'John',
          lastName: 'Doe',
          bio: 'Experienced project manager looking for quality freelancers',
          location: 'New York, USA'
        }
      },
      {
        username: 'alice_freelancer',
        email: '<EMAIL>',
        password: 'password123',
        walletAddress: '0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890ab',
        role: 'freelancer',
        profile: {
          firstName: 'Alice',
          lastName: 'Smith',
          bio: 'Full-stack developer with 5+ years experience',
          skills: ['React', 'Node.js', 'MongoDB', 'TypeScript'],
          hourlyRate: 75,
          location: 'San Francisco, USA'
        },
        reputation: {
          rating: 4.8,
          totalReviews: 24,
          completedProjects: 18
        }
      },
      {
        username: 'bob_both',
        email: '<EMAIL>',
        password: 'password123',
        walletAddress: '0x9876543210fedcba9876543210fedcba9876543210fedcba9876543210fedcba98',
        role: 'both',
        profile: {
          firstName: 'Bob',
          lastName: 'Wilson',
          bio: 'Designer and project manager',
          skills: ['UI/UX Design', 'Figma', 'Adobe Creative Suite'],
          hourlyRate: 60,
          location: 'London, UK'
        },
        reputation: {
          rating: 4.5,
          totalReviews: 12,
          completedProjects: 10
        }
      }
    ];
    
    // Create users (will hash passwords automatically)
    const createdUsers = [];
    for (const userData of sampleUsers) {
      try {
        const user = new User(userData);
        const savedUser = await user.save();
        createdUsers.push(savedUser);
        console.log(`✅ Created user: ${userData.username}`);
      } catch (error) {
        if (error.code === 11000) {
          console.log(`⚠️  User ${userData.username} already exists, skipping...`);
          const existingUser = await User.findOne({ username: userData.username });
          createdUsers.push(existingUser);
        } else {
          console.error(`❌ Error creating user ${userData.username}:`, error.message);
        }
      }
    }
    
    // Create sample escrow (if we have users)
    if (createdUsers.length >= 2) {
      console.log('📋 Creating sample escrow...');
      
      const sampleEscrow = {
        blockchainEscrowId: 1,
        client: createdUsers[0]._id, // John (client)
        freelancer: createdUsers[1]._id, // Alice (freelancer)
        title: 'E-commerce Website Development',
        description: 'Build a modern e-commerce website with React and Node.js. Include user authentication, product catalog, shopping cart, and payment integration.',
        category: 'web-development',
        skills: ['React', 'Node.js', 'MongoDB', 'Stripe API'],
        amount: 500000000000, // 5000 APT in octas
        deadline: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
        status: 'created'
      };
      
      try {
        const escrow = new Escrow(sampleEscrow);
        await escrow.save();
        console.log('✅ Created sample escrow');
      } catch (error) {
        console.log('⚠️  Sample escrow might already exist, skipping...');
      }
    }
    
    // Display database statistics
    console.log('\n📊 Database Statistics:');
    const userCount = await User.countDocuments();
    const escrowCount = await Escrow.countDocuments();
    
    console.log(`👥 Users: ${userCount}`);
    console.log(`📋 Escrows: ${escrowCount}`);
    
    console.log('\n🎉 Database setup completed successfully!');
    console.log('\n📝 Sample Login Credentials:');
    console.log('Client: <EMAIL> / password123');
    console.log('Freelancer: <EMAIL> / password123');
    console.log('Both: <EMAIL> / password123');
    
    console.log('\n🚀 You can now start the application with: npm run dev');
    
  } catch (error) {
    console.error('❌ Database setup failed:', error);
  } finally {
    // Close the connection
    await mongoose.connection.close();
    console.log('🔌 Database connection closed');
    process.exit(0);
  }
};

// Run the setup
if (require.main === module) {
  setupDatabase();
}

module.exports = setupDatabase;
