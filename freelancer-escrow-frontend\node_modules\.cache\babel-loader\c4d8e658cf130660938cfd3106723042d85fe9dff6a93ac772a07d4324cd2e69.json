{"ast": null, "code": "import { useContext } from '@rc-component/context';\nimport * as React from 'react';\nimport Cell from \"../Cell\";\nimport TableContext from \"../context/TableContext\";\nimport devRenderTimes from \"../hooks/useRenderTimes\";\nfunction ExpandedRow(props) {\n  if (process.env.NODE_ENV !== 'production') {\n    devRenderTimes(props);\n  }\n  var prefixCls = props.prefixCls,\n    children = props.children,\n    Component = props.component,\n    cellComponent = props.cellComponent,\n    className = props.className,\n    expanded = props.expanded,\n    colSpan = props.colSpan,\n    isEmpty = props.isEmpty,\n    _props$stickyOffset = props.stickyOffset,\n    stickyOffset = _props$stickyOffset === void 0 ? 0 : _props$stickyOffset;\n  var _useContext = useContext(TableContext, ['scrollbarSize', 'fixHeader', 'fixColumn', 'componentWidth', 'horizonScroll']),\n    scrollbarSize = _useContext.scrollbarSize,\n    fixHeader = _useContext.fixHeader,\n    fixColumn = _useContext.fixColumn,\n    componentWidth = _useContext.componentWidth,\n    horizonScroll = _useContext.horizonScroll;\n\n  // Cache render node\n  var contentNode = children;\n  if (isEmpty ? horizonScroll && componentWidth : fixColumn) {\n    contentNode = /*#__PURE__*/React.createElement(\"div\", {\n      style: {\n        width: componentWidth - stickyOffset - (fixHeader && !isEmpty ? scrollbarSize : 0),\n        position: 'sticky',\n        left: stickyOffset,\n        overflow: 'hidden'\n      },\n      className: \"\".concat(prefixCls, \"-expanded-row-fixed\")\n    }, contentNode);\n  }\n  return /*#__PURE__*/React.createElement(Component, {\n    className: className,\n    style: {\n      display: expanded ? null : 'none'\n    }\n  }, /*#__PURE__*/React.createElement(Cell, {\n    component: cellComponent,\n    prefixCls: prefixCls,\n    colSpan: colSpan\n  }, contentNode));\n}\nexport default ExpandedRow;", "map": {"version": 3, "names": ["useContext", "React", "Cell", "TableContext", "devRenderTimes", "ExpandedRow", "props", "process", "env", "NODE_ENV", "prefixCls", "children", "Component", "component", "cellComponent", "className", "expanded", "colSpan", "isEmpty", "_props$stickyOffset", "stickyOffset", "_useContext", "scrollbarSize", "fixHeader", "fixColumn", "componentWidth", "horizonScroll", "contentNode", "createElement", "style", "width", "position", "left", "overflow", "concat", "display"], "sources": ["C:/Users/<USER>/Desktop/Aptos/freelancer-escrow-frontend/node_modules/rc-table/es/Body/ExpandedRow.js"], "sourcesContent": ["import { useContext } from '@rc-component/context';\nimport * as React from 'react';\nimport Cell from \"../Cell\";\nimport TableContext from \"../context/TableContext\";\nimport devRenderTimes from \"../hooks/useRenderTimes\";\nfunction ExpandedRow(props) {\n  if (process.env.NODE_ENV !== 'production') {\n    devRenderTimes(props);\n  }\n  var prefixCls = props.prefixCls,\n    children = props.children,\n    Component = props.component,\n    cellComponent = props.cellComponent,\n    className = props.className,\n    expanded = props.expanded,\n    colSpan = props.colSpan,\n    isEmpty = props.isEmpty,\n    _props$stickyOffset = props.stickyOffset,\n    stickyOffset = _props$stickyOffset === void 0 ? 0 : _props$stickyOffset;\n  var _useContext = useContext(TableContext, ['scrollbarSize', 'fixHeader', 'fixColumn', 'componentWidth', 'horizonScroll']),\n    scrollbarSize = _useContext.scrollbarSize,\n    fixHeader = _useContext.fixHeader,\n    fixColumn = _useContext.fixColumn,\n    componentWidth = _useContext.componentWidth,\n    horizonScroll = _useContext.horizonScroll;\n\n  // Cache render node\n  var contentNode = children;\n  if (isEmpty ? horizonScroll && componentWidth : fixColumn) {\n    contentNode = /*#__PURE__*/React.createElement(\"div\", {\n      style: {\n        width: componentWidth - stickyOffset - (fixHeader && !isEmpty ? scrollbarSize : 0),\n        position: 'sticky',\n        left: stickyOffset,\n        overflow: 'hidden'\n      },\n      className: \"\".concat(prefixCls, \"-expanded-row-fixed\")\n    }, contentNode);\n  }\n  return /*#__PURE__*/React.createElement(Component, {\n    className: className,\n    style: {\n      display: expanded ? null : 'none'\n    }\n  }, /*#__PURE__*/React.createElement(Cell, {\n    component: cellComponent,\n    prefixCls: prefixCls,\n    colSpan: colSpan\n  }, contentNode));\n}\nexport default ExpandedRow;"], "mappings": "AAAA,SAASA,UAAU,QAAQ,uBAAuB;AAClD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,IAAI,MAAM,SAAS;AAC1B,OAAOC,YAAY,MAAM,yBAAyB;AAClD,OAAOC,cAAc,MAAM,yBAAyB;AACpD,SAASC,WAAWA,CAACC,KAAK,EAAE;EAC1B,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzCL,cAAc,CAACE,KAAK,CAAC;EACvB;EACA,IAAII,SAAS,GAAGJ,KAAK,CAACI,SAAS;IAC7BC,QAAQ,GAAGL,KAAK,CAACK,QAAQ;IACzBC,SAAS,GAAGN,KAAK,CAACO,SAAS;IAC3BC,aAAa,GAAGR,KAAK,CAACQ,aAAa;IACnCC,SAAS,GAAGT,KAAK,CAACS,SAAS;IAC3BC,QAAQ,GAAGV,KAAK,CAACU,QAAQ;IACzBC,OAAO,GAAGX,KAAK,CAACW,OAAO;IACvBC,OAAO,GAAGZ,KAAK,CAACY,OAAO;IACvBC,mBAAmB,GAAGb,KAAK,CAACc,YAAY;IACxCA,YAAY,GAAGD,mBAAmB,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,mBAAmB;EACzE,IAAIE,WAAW,GAAGrB,UAAU,CAACG,YAAY,EAAE,CAAC,eAAe,EAAE,WAAW,EAAE,WAAW,EAAE,gBAAgB,EAAE,eAAe,CAAC,CAAC;IACxHmB,aAAa,GAAGD,WAAW,CAACC,aAAa;IACzCC,SAAS,GAAGF,WAAW,CAACE,SAAS;IACjCC,SAAS,GAAGH,WAAW,CAACG,SAAS;IACjCC,cAAc,GAAGJ,WAAW,CAACI,cAAc;IAC3CC,aAAa,GAAGL,WAAW,CAACK,aAAa;;EAE3C;EACA,IAAIC,WAAW,GAAGhB,QAAQ;EAC1B,IAAIO,OAAO,GAAGQ,aAAa,IAAID,cAAc,GAAGD,SAAS,EAAE;IACzDG,WAAW,GAAG,aAAa1B,KAAK,CAAC2B,aAAa,CAAC,KAAK,EAAE;MACpDC,KAAK,EAAE;QACLC,KAAK,EAAEL,cAAc,GAAGL,YAAY,IAAIG,SAAS,IAAI,CAACL,OAAO,GAAGI,aAAa,GAAG,CAAC,CAAC;QAClFS,QAAQ,EAAE,QAAQ;QAClBC,IAAI,EAAEZ,YAAY;QAClBa,QAAQ,EAAE;MACZ,CAAC;MACDlB,SAAS,EAAE,EAAE,CAACmB,MAAM,CAACxB,SAAS,EAAE,qBAAqB;IACvD,CAAC,EAAEiB,WAAW,CAAC;EACjB;EACA,OAAO,aAAa1B,KAAK,CAAC2B,aAAa,CAAChB,SAAS,EAAE;IACjDG,SAAS,EAAEA,SAAS;IACpBc,KAAK,EAAE;MACLM,OAAO,EAAEnB,QAAQ,GAAG,IAAI,GAAG;IAC7B;EACF,CAAC,EAAE,aAAaf,KAAK,CAAC2B,aAAa,CAAC1B,IAAI,EAAE;IACxCW,SAAS,EAAEC,aAAa;IACxBJ,SAAS,EAAEA,SAAS;IACpBO,OAAO,EAAEA;EACX,CAAC,EAAEU,WAAW,CAAC,CAAC;AAClB;AACA,eAAetB,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}