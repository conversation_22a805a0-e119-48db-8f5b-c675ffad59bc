module freelancer_escrow::escrow {
    use std::signer;
    use std::string::{Self, String};
    use std::vector;
    use aptos_framework::coin::{Self, Coin};
    use aptos_framework::aptos_coin::AptosCoin;
    use aptos_framework::timestamp;
    use aptos_framework::event::{Self, EventHandle};
    use aptos_framework::account;
    use freelancer_escrow::security;

    /// Error codes
    const E_NOT_AUTHORIZED: u64 = 1;
    const E_ESCROW_NOT_FOUND: u64 = 2;
    const E_INVALID_STATUS: u64 = 3;
    const E_INSUFFICIENT_FUNDS: u64 = 4;
    const E_DEADLINE_PASSED: u64 = 5;
    const E_DEADLINE_NOT_PASSED: u64 = 6;
    const E_ALREADY_EXISTS: u64 = 7;
    const E_INVALID_AMOUNT: u64 = 8;
    const E_RATE_LIMITED: u64 = 9;
    const E_BLACKLISTED: u64 = 10;
    const E_SECURITY_VIOLATION: u64 = 11;

    /// Escrow status constants
    const STATUS_CREATED: u8 = 0;
    const STATUS_FUNDED: u8 = 1;
    const STATUS_WORK_SUBMITTED: u8 = 2;
    const STATUS_COMPLETED: u8 = 3;
    const STATUS_DISPUTED: u8 = 4;
    const STATUS_CANCELLED: u8 = 5;

    /// Escrow agreement structure
    struct EscrowAgreement has key, store {
        escrow_id: u64,
        client: address,
        freelancer: address,
        amount: u64,
        title: String,
        description: String,
        status: u8,
        deadline: u64,
        work_submission: String,
        created_at: u64,
        funds: Coin<AptosCoin>,
    }

    /// Global escrow registry
    struct EscrowRegistry has key {
        escrows: vector<u64>,
        next_escrow_id: u64,
        admin: address,
        security_admin: address,
        // Events
        escrow_created_events: EventHandle<EscrowCreatedEvent>,
        escrow_funded_events: EventHandle<EscrowFundedEvent>,
        work_submitted_events: EventHandle<WorkSubmittedEvent>,
        escrow_completed_events: EventHandle<EscrowCompletedEvent>,
        escrow_disputed_events: EventHandle<EscrowDisputedEvent>,
        escrow_cancelled_events: EventHandle<EscrowCancelledEvent>,
    }

    /// Event structures
    struct EscrowCreatedEvent has drop, store {
        escrow_id: u64,
        client: address,
        freelancer: address,
        amount: u64,
        title: String,
        deadline: u64,
    }

    struct EscrowFundedEvent has drop, store {
        escrow_id: u64,
        amount: u64,
    }

    struct WorkSubmittedEvent has drop, store {
        escrow_id: u64,
        freelancer: address,
        submission: String,
    }

    struct EscrowCompletedEvent has drop, store {
        escrow_id: u64,
        freelancer: address,
        amount: u64,
    }

    struct EscrowDisputedEvent has drop, store {
        escrow_id: u64,
        disputed_by: address,
    }

    struct EscrowCancelledEvent has drop, store {
        escrow_id: u64,
        refunded_to: address,
        amount: u64,
    }

    /// Initialize the escrow system
    public entry fun initialize(admin: &signer, security_admin: address) {
        let admin_addr = signer::address_of(admin);

        assert!(!exists<EscrowRegistry>(admin_addr), E_ALREADY_EXISTS);

        move_to(admin, EscrowRegistry {
            escrows: vector::empty<u64>(),
            next_escrow_id: 1,
            admin: admin_addr,
            security_admin,
            escrow_created_events: account::new_event_handle<EscrowCreatedEvent>(admin),
            escrow_funded_events: account::new_event_handle<EscrowFundedEvent>(admin),
            work_submitted_events: account::new_event_handle<WorkSubmittedEvent>(admin),
            escrow_completed_events: account::new_event_handle<EscrowCompletedEvent>(admin),
            escrow_disputed_events: account::new_event_handle<EscrowDisputedEvent>(admin),
            escrow_cancelled_events: account::new_event_handle<EscrowCancelledEvent>(admin),
        });
    }

    /// Create a new escrow agreement
    public entry fun create_escrow(
        client: &signer,
        freelancer: address,
        amount: u64,
        title: String,
        description: String,
        deadline_hours: u64,
        registry_addr: address,
    ) acquires EscrowRegistry {
        let client_addr = signer::address_of(client);
        assert!(amount > 0, E_INVALID_AMOUNT);

        let registry = borrow_global_mut<EscrowRegistry>(registry_addr);

        // Security checks
        assert!(!security::is_blacklisted(client_addr, registry.security_admin), E_BLACKLISTED);
        assert!(!security::is_blacklisted(freelancer, registry.security_admin), E_BLACKLISTED);
        assert!(security::validate_amount(amount, registry.security_admin), E_INVALID_AMOUNT);
        assert!(security::check_rate_limit(client_addr, registry.security_admin), E_RATE_LIMITED);

        // Initialize rate limiting for client if needed
        security::initialize_rate_limiting(client);
        security::record_escrow_creation(client_addr);

        let escrow_id = registry.next_escrow_id;
        registry.next_escrow_id = registry.next_escrow_id + 1;
        
        let deadline = timestamp::now_seconds() + (deadline_hours * 3600);
        
        let escrow = EscrowAgreement {
            escrow_id,
            client: client_addr,
            freelancer,
            amount,
            title: title,
            description: description,
            status: STATUS_CREATED,
            deadline,
            work_submission: string::utf8(b""),
            created_at: timestamp::now_seconds(),
            funds: coin::zero<AptosCoin>(),
        };
        
        vector::push_back(&mut registry.escrows, escrow_id);
        
        // Emit event
        event::emit_event(&mut registry.escrow_created_events, EscrowCreatedEvent {
            escrow_id,
            client: client_addr,
            freelancer,
            amount,
            title,
            deadline,
        });
        
        move_to(client, escrow);
    }

    /// Fund the escrow with the agreed amount
    public entry fun fund_escrow(
        client: &signer,
        escrow_id: u64,
        registry_addr: address,
    ) acquires EscrowAgreement, EscrowRegistry {
        let client_addr = signer::address_of(client);
        assert!(exists<EscrowAgreement>(client_addr), E_ESCROW_NOT_FOUND);
        
        let escrow = borrow_global_mut<EscrowAgreement>(client_addr);
        assert!(escrow.escrow_id == escrow_id, E_ESCROW_NOT_FOUND);
        assert!(escrow.client == client_addr, E_NOT_AUTHORIZED);
        assert!(escrow.status == STATUS_CREATED, E_INVALID_STATUS);
        
        let payment = coin::withdraw<AptosCoin>(client, escrow.amount);
        coin::merge(&mut escrow.funds, payment);
        escrow.status = STATUS_FUNDED;
        
        // Emit event
        let registry = borrow_global_mut<EscrowRegistry>(registry_addr);
        event::emit_event(&mut registry.escrow_funded_events, EscrowFundedEvent {
            escrow_id,
            amount: escrow.amount,
        });
    }

    /// Submit work by freelancer
    public entry fun submit_work(
        freelancer: &signer,
        client_addr: address,
        escrow_id: u64,
        work_submission: String,
        registry_addr: address,
    ) acquires EscrowAgreement, EscrowRegistry {
        let freelancer_addr = signer::address_of(freelancer);
        assert!(exists<EscrowAgreement>(client_addr), E_ESCROW_NOT_FOUND);
        
        let escrow = borrow_global_mut<EscrowAgreement>(client_addr);
        assert!(escrow.escrow_id == escrow_id, E_ESCROW_NOT_FOUND);
        assert!(escrow.freelancer == freelancer_addr, E_NOT_AUTHORIZED);
        assert!(escrow.status == STATUS_FUNDED, E_INVALID_STATUS);
        assert!(timestamp::now_seconds() <= escrow.deadline, E_DEADLINE_PASSED);
        
        escrow.work_submission = work_submission;
        escrow.status = STATUS_WORK_SUBMITTED;
        
        // Emit event
        let registry = borrow_global_mut<EscrowRegistry>(registry_addr);
        event::emit_event(&mut registry.work_submitted_events, WorkSubmittedEvent {
            escrow_id,
            freelancer: freelancer_addr,
            submission: work_submission,
        });
    }

    /// Approve work and release payment
    public entry fun approve_and_release(
        client: &signer,
        escrow_id: u64,
        registry_addr: address,
    ) acquires EscrowAgreement, EscrowRegistry {
        let client_addr = signer::address_of(client);
        assert!(exists<EscrowAgreement>(client_addr), E_ESCROW_NOT_FOUND);
        
        let escrow = borrow_global_mut<EscrowAgreement>(client_addr);
        assert!(escrow.escrow_id == escrow_id, E_ESCROW_NOT_FOUND);
        assert!(escrow.client == client_addr, E_NOT_AUTHORIZED);
        assert!(escrow.status == STATUS_WORK_SUBMITTED, E_INVALID_STATUS);
        
        let payment = coin::extract_all(&mut escrow.funds);
        coin::deposit(escrow.freelancer, payment);
        escrow.status = STATUS_COMPLETED;
        
        // Emit event
        let registry = borrow_global_mut<EscrowRegistry>(registry_addr);
        event::emit_event(&mut registry.escrow_completed_events, EscrowCompletedEvent {
            escrow_id,
            freelancer: escrow.freelancer,
            amount: escrow.amount,
        });
    }

    /// Dispute an escrow (can be called by client or freelancer)
    public entry fun dispute_escrow(
        disputer: &signer,
        client_addr: address,
        escrow_id: u64,
        registry_addr: address,
    ) acquires EscrowAgreement, EscrowRegistry {
        let disputer_addr = signer::address_of(disputer);
        assert!(exists<EscrowAgreement>(client_addr), E_ESCROW_NOT_FOUND);

        let escrow = borrow_global_mut<EscrowAgreement>(client_addr);
        assert!(escrow.escrow_id == escrow_id, E_ESCROW_NOT_FOUND);
        assert!(
            escrow.client == disputer_addr || escrow.freelancer == disputer_addr,
            E_NOT_AUTHORIZED
        );
        assert!(
            escrow.status == STATUS_FUNDED || escrow.status == STATUS_WORK_SUBMITTED,
            E_INVALID_STATUS
        );

        escrow.status = STATUS_DISPUTED;

        // Emit event
        let registry = borrow_global_mut<EscrowRegistry>(registry_addr);
        event::emit_event(&mut registry.escrow_disputed_events, EscrowDisputedEvent {
            escrow_id,
            disputed_by: disputer_addr,
        });
    }

    /// Cancel escrow and refund (only if deadline passed and no work submitted)
    public entry fun cancel_escrow(
        client: &signer,
        escrow_id: u64,
        registry_addr: address,
    ) acquires EscrowAgreement, EscrowRegistry {
        let client_addr = signer::address_of(client);
        assert!(exists<EscrowAgreement>(client_addr), E_ESCROW_NOT_FOUND);

        let escrow = borrow_global_mut<EscrowAgreement>(client_addr);
        assert!(escrow.escrow_id == escrow_id, E_ESCROW_NOT_FOUND);
        assert!(escrow.client == client_addr, E_NOT_AUTHORIZED);
        assert!(escrow.status == STATUS_FUNDED, E_INVALID_STATUS);
        assert!(timestamp::now_seconds() > escrow.deadline, E_DEADLINE_NOT_PASSED);

        let refund = coin::extract_all(&mut escrow.funds);
        coin::deposit(client_addr, refund);
        escrow.status = STATUS_CANCELLED;

        // Emit event
        let registry = borrow_global_mut<EscrowRegistry>(registry_addr);
        event::emit_event(&mut registry.escrow_cancelled_events, EscrowCancelledEvent {
            escrow_id,
            refunded_to: client_addr,
            amount: escrow.amount,
        });
    }

    /// Admin function to resolve disputes
    public entry fun resolve_dispute(
        admin: &signer,
        client_addr: address,
        escrow_id: u64,
        pay_freelancer: bool,
        registry_addr: address,
    ) acquires EscrowAgreement, EscrowRegistry {
        let admin_addr = signer::address_of(admin);
        let registry = borrow_global<EscrowRegistry>(registry_addr);
        assert!(registry.admin == admin_addr, E_NOT_AUTHORIZED);

        assert!(exists<EscrowAgreement>(client_addr), E_ESCROW_NOT_FOUND);
        let escrow = borrow_global_mut<EscrowAgreement>(client_addr);
        assert!(escrow.escrow_id == escrow_id, E_ESCROW_NOT_FOUND);
        assert!(escrow.status == STATUS_DISPUTED, E_INVALID_STATUS);

        let payment = coin::extract_all(&mut escrow.funds);

        if (pay_freelancer) {
            coin::deposit(escrow.freelancer, payment);
            escrow.status = STATUS_COMPLETED;

            let registry_mut = borrow_global_mut<EscrowRegistry>(registry_addr);
            event::emit_event(&mut registry_mut.escrow_completed_events, EscrowCompletedEvent {
                escrow_id,
                freelancer: escrow.freelancer,
                amount: escrow.amount,
            });
        } else {
            coin::deposit(escrow.client, payment);
            escrow.status = STATUS_CANCELLED;

            let registry_mut = borrow_global_mut<EscrowRegistry>(registry_addr);
            event::emit_event(&mut registry_mut.escrow_cancelled_events, EscrowCancelledEvent {
                escrow_id,
                refunded_to: escrow.client,
                amount: escrow.amount,
            });
        }
    }

    /// View functions
    #[view]
    public fun get_escrow_details(client_addr: address, escrow_id: u64): (
        address, address, u64, String, String, u8, u64, String, u64, u64
    ) acquires EscrowAgreement {
        assert!(exists<EscrowAgreement>(client_addr), E_ESCROW_NOT_FOUND);
        let escrow = borrow_global<EscrowAgreement>(client_addr);
        assert!(escrow.escrow_id == escrow_id, E_ESCROW_NOT_FOUND);

        (
            escrow.client,
            escrow.freelancer,
            escrow.amount,
            escrow.title,
            escrow.description,
            escrow.status,
            escrow.deadline,
            escrow.work_submission,
            escrow.created_at,
            coin::value(&escrow.funds)
        )
    }

    #[view]
    public fun get_escrow_status(client_addr: address, escrow_id: u64): u8 acquires EscrowAgreement {
        assert!(exists<EscrowAgreement>(client_addr), E_ESCROW_NOT_FOUND);
        let escrow = borrow_global<EscrowAgreement>(client_addr);
        assert!(escrow.escrow_id == escrow_id, E_ESCROW_NOT_FOUND);
        escrow.status
    }

    #[view]
    public fun get_all_escrows(registry_addr: address): vector<u64> acquires EscrowRegistry {
        let registry = borrow_global<EscrowRegistry>(registry_addr);
        registry.escrows
    }

    #[view]
    public fun is_deadline_passed(client_addr: address, escrow_id: u64): bool acquires EscrowAgreement {
        assert!(exists<EscrowAgreement>(client_addr), E_ESCROW_NOT_FOUND);
        let escrow = borrow_global<EscrowAgreement>(client_addr);
        assert!(escrow.escrow_id == escrow_id, E_ESCROW_NOT_FOUND);
        timestamp::now_seconds() > escrow.deadline
    }
}
