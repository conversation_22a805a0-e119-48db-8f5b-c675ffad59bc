# MongoDB Configuration Options for FreelanceEscrow
# Copy the appropriate configuration to your .env file

# ===========================================
# LOCAL MONGODB (Default - No Authentication)
# ===========================================
# Use this if you have MongoDB installed locally without authentication
MONGODB_URI=mongodb://localhost:27017/freelancer-escrow

# ===========================================
# LOCAL MONGODB (With Authentication)
# ===========================================
# Use this if you have MongoDB with username/password authentication
# Replace 'username' and 'password' with your actual credentials
# MONGODB_URI=*************************************************************

# ===========================================
# MONGODB COMPASS (Default Connection)
# ===========================================
# This is the same as the local connection above
# MongoDB Compass typically connects to localhost:27017 by default
MONGODB_URI=mongodb://localhost:27017/freelancer-escrow

# ===========================================
# MONGODB ATLAS (Cloud Database)
# ===========================================
# Use this if you're using MongoDB Atlas (cloud service)
# Replace the connection string with your actual Atlas connection string
# MONGODB_URI=mongodb+srv://username:<EMAIL>/freelancer-escrow?retryWrites=true&w=majority

# ===========================================
# DOCKER MONGODB
# ===========================================
# Use this if you're running MongoDB in Docker
# MONGODB_URI=mongodb://localhost:27017/freelancer-escrow

# ===========================================
# CUSTOM HOST/PORT
# ===========================================
# Use this if MongoDB is running on a different host or port
# MONGODB_URI=mongodb://your-host:your-port/freelancer-escrow

# ===========================================
# CONNECTION OPTIONS
# ===========================================
# You can add connection options to any URI above:
# ?retryWrites=true&w=majority&authSource=admin

# ===========================================
# INSTRUCTIONS
# ===========================================
# 1. Choose the appropriate connection string above
# 2. Copy it to your .env file
# 3. Replace MONGODB_URI= with your chosen option
# 4. Make sure MongoDB is running before starting the application
# 5. Test connection with: npm run dev

# ===========================================
# TROUBLESHOOTING
# ===========================================
# If you get connection errors:
# 1. Make sure MongoDB is running (check MongoDB Compass)
# 2. Verify the database name matches your setup
# 3. Check if authentication is required
# 4. Ensure the port (27017) is not blocked by firewall
# 5. For Atlas, check your IP whitelist settings
