module freelancer_escrow::security {
    use std::signer;
    use std::vector;
    use aptos_framework::timestamp;
    use aptos_framework::event::{Self, EventHandle};
    use aptos_framework::account;

    /// Error codes
    const E_NOT_AUTHORIZED: u64 = 1;
    const E_RATE_LIMITED: u64 = 2;
    const E_BLACKLISTED: u64 = 3;
    const E_ALREADY_EXISTS: u64 = 4;
    const E_NOT_FOUND: u64 = 5;

    /// Security configuration
    struct SecurityConfig has key {
        admin: address,
        max_escrows_per_hour: u64,
        min_escrow_amount: u64,
        max_escrow_amount: u64,
        dispute_timeout_hours: u64,
        blacklisted_addresses: vector<address>,
        // Events
        security_violation_events: EventHandle<SecurityViolationEvent>,
        blacklist_events: EventHandle<BlacklistEvent>,
    }

    /// Rate limiting tracker
    struct RateLimitTracker has key {
        escrows_created: vector<u64>, // timestamps
        last_cleanup: u64,
    }

    /// Security violation event
    struct SecurityViolationEvent has drop, store {
        violator: address,
        violation_type: u8,
        timestamp: u64,
    }

    /// Blacklist event
    struct BlacklistEvent has drop, store {
        address: address,
        blacklisted: bool,
        reason: vector<u8>,
    }

    /// Violation types
    const VIOLATION_RATE_LIMIT: u8 = 1;
    const VIOLATION_SUSPICIOUS_AMOUNT: u8 = 2;
    const VIOLATION_REPEATED_DISPUTES: u8 = 3;

    /// Initialize security system
    public entry fun initialize_security(admin: &signer) {
        let admin_addr = signer::address_of(admin);
        assert!(!exists<SecurityConfig>(admin_addr), E_ALREADY_EXISTS);
        
        move_to(admin, SecurityConfig {
            admin: admin_addr,
            max_escrows_per_hour: 10,
            min_escrow_amount: 1000000, // 0.01 APT in octas
            max_escrow_amount: *************, // 10,000 APT in octas
            dispute_timeout_hours: 168, // 7 days
            blacklisted_addresses: vector::empty<address>(),
            security_violation_events: account::new_event_handle<SecurityViolationEvent>(admin),
            blacklist_events: account::new_event_handle<BlacklistEvent>(admin),
        });
    }

    /// Initialize rate limiting for a user
    public entry fun initialize_rate_limiting(user: &signer) {
        let user_addr = signer::address_of(user);
        if (!exists<RateLimitTracker>(user_addr)) {
            move_to(user, RateLimitTracker {
                escrows_created: vector::empty<u64>(),
                last_cleanup: timestamp::now_seconds(),
            });
        }
    }

    /// Check if user can create escrow (rate limiting)
    public fun check_rate_limit(user_addr: address, security_admin: address): bool acquires RateLimitTracker, SecurityConfig {
        if (!exists<RateLimitTracker>(user_addr)) {
            return true
        };
        
        let tracker = borrow_global_mut<RateLimitTracker>(user_addr);
        let config = borrow_global<SecurityConfig>(security_admin);
        let now = timestamp::now_seconds();
        
        // Clean up old entries (older than 1 hour)
        let hour_ago = if (now >= 3600) { now - 3600 } else { 0 };
        let new_escrows = vector::empty<u64>();
        let i = 0;
        let len = vector::length(&tracker.escrows_created);
        
        while (i < len) {
            let timestamp = *vector::borrow(&tracker.escrows_created, i);
            if (timestamp > hour_ago) {
                vector::push_back(&mut new_escrows, timestamp);
            };
            i = i + 1;
        };
        
        tracker.escrows_created = new_escrows;
        tracker.last_cleanup = now;
        
        // Check if under rate limit
        let current_count = vector::length(&tracker.escrows_created);
        current_count < config.max_escrows_per_hour
    }

    /// Record escrow creation for rate limiting
    public fun record_escrow_creation(user_addr: address) acquires RateLimitTracker {
        if (!exists<RateLimitTracker>(user_addr)) {
            return
        };
        
        let tracker = borrow_global_mut<RateLimitTracker>(user_addr);
        vector::push_back(&mut tracker.escrows_created, timestamp::now_seconds());
    }

    /// Check if address is blacklisted
    public fun is_blacklisted(addr: address, security_admin: address): bool acquires SecurityConfig {
        let config = borrow_global<SecurityConfig>(security_admin);
        vector::contains(&config.blacklisted_addresses, &addr)
    }

    /// Validate escrow amount
    public fun validate_amount(amount: u64, security_admin: address): bool acquires SecurityConfig {
        let config = borrow_global<SecurityConfig>(security_admin);
        amount >= config.min_escrow_amount && amount <= config.max_escrow_amount
    }

    /// Add address to blacklist (admin only)
    public entry fun blacklist_address(
        admin: &signer,
        target_addr: address,
        reason: vector<u8>,
        security_admin: address,
    ) acquires SecurityConfig {
        let admin_addr = signer::address_of(admin);
        let config = borrow_global_mut<SecurityConfig>(security_admin);
        assert!(config.admin == admin_addr, E_NOT_AUTHORIZED);
        
        if (!vector::contains(&config.blacklisted_addresses, &target_addr)) {
            vector::push_back(&mut config.blacklisted_addresses, target_addr);
            
            event::emit_event(&mut config.blacklist_events, BlacklistEvent {
                address: target_addr,
                blacklisted: true,
                reason,
            });
        }
    }

    /// Remove address from blacklist (admin only)
    public entry fun unblacklist_address(
        admin: &signer,
        target_addr: address,
        security_admin: address,
    ) acquires SecurityConfig {
        let admin_addr = signer::address_of(admin);
        let config = borrow_global_mut<SecurityConfig>(security_admin);
        assert!(config.admin == admin_addr, E_NOT_AUTHORIZED);
        
        let (found, index) = vector::index_of(&config.blacklisted_addresses, &target_addr);
        if (found) {
            vector::remove(&mut config.blacklisted_addresses, index);
            
            event::emit_event(&mut config.blacklist_events, BlacklistEvent {
                address: target_addr,
                blacklisted: false,
                reason: b"Removed from blacklist",
            });
        }
    }

    /// Report security violation
    public fun report_violation(
        violator: address,
        violation_type: u8,
        security_admin: address,
    ) acquires SecurityConfig {
        let config = borrow_global_mut<SecurityConfig>(security_admin);
        
        event::emit_event(&mut config.security_violation_events, SecurityViolationEvent {
            violator,
            violation_type,
            timestamp: timestamp::now_seconds(),
        });
    }

    /// Update security configuration (admin only)
    public entry fun update_config(
        admin: &signer,
        max_escrows_per_hour: u64,
        min_escrow_amount: u64,
        max_escrow_amount: u64,
        dispute_timeout_hours: u64,
        security_admin: address,
    ) acquires SecurityConfig {
        let admin_addr = signer::address_of(admin);
        let config = borrow_global_mut<SecurityConfig>(security_admin);
        assert!(config.admin == admin_addr, E_NOT_AUTHORIZED);
        
        config.max_escrows_per_hour = max_escrows_per_hour;
        config.min_escrow_amount = min_escrow_amount;
        config.max_escrow_amount = max_escrow_amount;
        config.dispute_timeout_hours = dispute_timeout_hours;
    }

    /// View functions
    #[view]
    public fun get_security_config(security_admin: address): (u64, u64, u64, u64) acquires SecurityConfig {
        let config = borrow_global<SecurityConfig>(security_admin);
        (
            config.max_escrows_per_hour,
            config.min_escrow_amount,
            config.max_escrow_amount,
            config.dispute_timeout_hours
        )
    }

    #[view]
    public fun get_user_rate_limit_status(user_addr: address): (u64, u64) acquires RateLimitTracker {
        if (!exists<RateLimitTracker>(user_addr)) {
            return (0, 0)
        };
        
        let tracker = borrow_global<RateLimitTracker>(user_addr);
        (vector::length(&tracker.escrows_created), tracker.last_cleanup)
    }

    #[view]
    public fun get_blacklisted_addresses(security_admin: address): vector<address> acquires SecurityConfig {
        let config = borrow_global<SecurityConfig>(security_admin);
        config.blacklisted_addresses
    }
}
