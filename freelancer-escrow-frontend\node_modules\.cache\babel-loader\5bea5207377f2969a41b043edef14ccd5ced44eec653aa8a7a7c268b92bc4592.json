{"ast": null, "code": "export {};", "map": {"version": 3, "names": [], "sources": ["C:\\Users\\<USER>\\Desktop\\Aptos\\freelancer-escrow-frontend\\node_modules\\@wallet-standard\\base\\src\\bytes.ts"], "sourcesContent": ["type TypedArrayMutableProperties = 'copyWithin' | 'fill' | 'reverse' | 'set' | 'sort';\nexport interface ReadonlyUint8Array extends Omit<Uint8Array, TypedArrayMutableProperties> {\n    readonly [n: number]: number;\n}\n\nexport {};\n"], "mappings": "", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}