# MongoDB Setup Guide for FreelanceEscrow

## 🚀 Quick Start

### Option 1: Use MongoDB Compass (Recommended)

1. **Download and Install MongoDB Compass**
   - Go to https://www.mongodb.com/products/compass
   - Download and install MongoDB Compass
   - This will also install MongoDB Community Server

2. **Start MongoDB**
   - MongoDB should start automatically after installation
   - If not, start it manually through Services (Windows) or System Preferences (Mac)

3. **Connect with Compass**
   - Open MongoDB Compass
   - Use default connection: `mongodb://localhost:27017`
   - Click "Connect"

4. **Setup Database**
   ```bash
   cd freelancer-escrow-backend
   npm run setup-db
   ```

### Option 2: MongoDB Atlas (Cloud)

1. **Create Atlas Account**
   - Go to https://www.mongodb.com/atlas
   - Create a free account
   - Create a new cluster

2. **Get Connection String**
   - Click "Connect" on your cluster
   - Choose "Connect your application"
   - Copy the connection string

3. **Update Environment**
   - Edit `.env` file
   - Replace `MONGODB_URI` with your Atlas connection string
   - Example: `mongodb+srv://username:<EMAIL>/freelancer-escrow`

## 🔧 Configuration Options

### Local MongoDB (Default)
```env
MONGODB_URI=mongodb://localhost:27017/freelancer-escrow
```

### With Authentication
```env
MONGODB_URI=*************************************************************
```

### MongoDB Atlas
```env
MONGODB_URI=mongodb+srv://username:<EMAIL>/freelancer-escrow
```

## 📋 Database Setup

### Automatic Setup (Recommended)
```bash
# Navigate to backend directory
cd freelancer-escrow-backend

# Install dependencies (if not done already)
npm install

# Setup database with sample data
npm run setup-db
```

### Manual Setup
1. Start MongoDB service
2. Create database named `freelancer-escrow`
3. The application will create collections automatically

## 🧪 Sample Data

The setup script creates:

### Sample Users
- **Client**: <EMAIL> / password123
- **Freelancer**: <EMAIL> / password123  
- **Both**: <EMAIL> / password123

### Sample Escrow
- E-commerce website development project
- 5000 APT value
- 30-day deadline

## 🔍 Verification

### Check Connection
```bash
# Test API health (backend should be running)
curl http://localhost:5000/api/health
```

### MongoDB Compass
1. Open MongoDB Compass
2. Connect to `mongodb://localhost:27017`
3. Look for `freelancer-escrow` database
4. Check `users` and `escrows` collections

### Application Test
1. Start backend: `npm run dev`
2. Start frontend: `cd ../freelancer-escrow-frontend && npm start`
3. Try logging in with sample credentials

## 🚨 Troubleshooting

### Connection Refused
- Make sure MongoDB service is running
- Check if port 27017 is available
- Verify firewall settings

### Authentication Failed
- Check username/password in connection string
- Verify database permissions
- For Atlas: check IP whitelist

### Database Not Found
- Run the setup script: `npm run setup-db`
- Check database name in connection string
- Verify MongoDB is running

### Common Errors

**Error: connect ECONNREFUSED**
```bash
# Start MongoDB service
# Windows: Start "MongoDB" service in Services
# Mac: brew services start mongodb-community
# Linux: sudo systemctl start mongod
```

**Error: Authentication failed**
```bash
# Check your .env file
# Verify username/password
# For local MongoDB, try without authentication first
```

## 📚 Additional Resources

- [MongoDB Installation Guide](https://docs.mongodb.com/manual/installation/)
- [MongoDB Compass Documentation](https://docs.mongodb.com/compass/)
- [MongoDB Atlas Getting Started](https://docs.atlas.mongodb.com/getting-started/)

## 🆘 Need Help?

If you encounter issues:
1. Check the error messages in terminal
2. Verify MongoDB is running in Compass
3. Test connection with the setup script
4. Check firewall and network settings
