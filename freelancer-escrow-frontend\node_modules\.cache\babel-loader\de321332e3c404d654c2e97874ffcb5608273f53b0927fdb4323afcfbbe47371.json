{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport useMemo from \"rc-util/es/hooks/useMemo\";\nimport isEqual from \"rc-util/es/isEqual\";\nimport getValue from \"rc-util/es/utils/get\";\nimport warning from \"rc-util/es/warning\";\nimport * as React from 'react';\nimport PerfContext from \"../context/PerfContext\";\nimport { validateValue } from \"../utils/valueUtil\";\nimport { useImmutableMark } from \"../context/TableContext\";\nfunction isRenderCell(data) {\n  return data && _typeof(data) === 'object' && !Array.isArray(data) && ! /*#__PURE__*/React.isValidElement(data);\n}\nexport default function useCellRender(record, dataIndex, renderIndex, children, render, shouldCellUpdate) {\n  // TODO: Remove this after next major version\n  var perfRecord = React.useContext(PerfContext);\n  var mark = useImmutableMark();\n\n  // ======================== Render ========================\n  var retData = useMemo(function () {\n    if (validateValue(children)) {\n      return [children];\n    }\n    var path = dataIndex === null || dataIndex === undefined || dataIndex === '' ? [] : Array.isArray(dataIndex) ? dataIndex : [dataIndex];\n    var value = getValue(record, path);\n\n    // Customize render node\n    var returnChildNode = value;\n    var returnCellProps = undefined;\n    if (render) {\n      var renderData = render(value, record, renderIndex);\n      if (isRenderCell(renderData)) {\n        if (process.env.NODE_ENV !== 'production') {\n          warning(false, '`columns.render` return cell props is deprecated with perf issue, please use `onCell` instead.');\n        }\n        returnChildNode = renderData.children;\n        returnCellProps = renderData.props;\n        perfRecord.renderWithProps = true;\n      } else {\n        returnChildNode = renderData;\n      }\n    }\n    return [returnChildNode, returnCellProps];\n  }, [\n  // Force update deps\n  mark,\n  // Normal deps\n  record, children, dataIndex, render, renderIndex], function (prev, next) {\n    if (shouldCellUpdate) {\n      var _prev = _slicedToArray(prev, 2),\n        prevRecord = _prev[1];\n      var _next = _slicedToArray(next, 2),\n        nextRecord = _next[1];\n      return shouldCellUpdate(nextRecord, prevRecord);\n    }\n\n    // Legacy mode should always update\n    if (perfRecord.renderWithProps) {\n      return true;\n    }\n    return !isEqual(prev, next, true);\n  });\n  return retData;\n}", "map": {"version": 3, "names": ["_slicedToArray", "_typeof", "useMemo", "isEqual", "getValue", "warning", "React", "PerfContext", "validate<PERSON><PERSON>ue", "useImmutableMark", "isRenderCell", "data", "Array", "isArray", "isValidElement", "useCellRender", "record", "dataIndex", "renderIndex", "children", "render", "shouldCellUpdate", "perfRecord", "useContext", "mark", "retData", "path", "undefined", "value", "returnChildNode", "returnCellProps", "renderData", "process", "env", "NODE_ENV", "props", "renderWithProps", "prev", "next", "_prev", "prevRecord", "_next", "nextRecord"], "sources": ["C:/Users/<USER>/Desktop/Aptos/freelancer-escrow-frontend/node_modules/rc-table/es/Cell/useCellRender.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport useMemo from \"rc-util/es/hooks/useMemo\";\nimport isEqual from \"rc-util/es/isEqual\";\nimport getValue from \"rc-util/es/utils/get\";\nimport warning from \"rc-util/es/warning\";\nimport * as React from 'react';\nimport PerfContext from \"../context/PerfContext\";\nimport { validateValue } from \"../utils/valueUtil\";\nimport { useImmutableMark } from \"../context/TableContext\";\nfunction isRenderCell(data) {\n  return data && _typeof(data) === 'object' && !Array.isArray(data) && ! /*#__PURE__*/React.isValidElement(data);\n}\nexport default function useCellRender(record, dataIndex, renderIndex, children, render, shouldCellUpdate) {\n  // TODO: Remove this after next major version\n  var perfRecord = React.useContext(PerfContext);\n  var mark = useImmutableMark();\n\n  // ======================== Render ========================\n  var retData = useMemo(function () {\n    if (validateValue(children)) {\n      return [children];\n    }\n    var path = dataIndex === null || dataIndex === undefined || dataIndex === '' ? [] : Array.isArray(dataIndex) ? dataIndex : [dataIndex];\n    var value = getValue(record, path);\n\n    // Customize render node\n    var returnChildNode = value;\n    var returnCellProps = undefined;\n    if (render) {\n      var renderData = render(value, record, renderIndex);\n      if (isRenderCell(renderData)) {\n        if (process.env.NODE_ENV !== 'production') {\n          warning(false, '`columns.render` return cell props is deprecated with perf issue, please use `onCell` instead.');\n        }\n        returnChildNode = renderData.children;\n        returnCellProps = renderData.props;\n        perfRecord.renderWithProps = true;\n      } else {\n        returnChildNode = renderData;\n      }\n    }\n    return [returnChildNode, returnCellProps];\n  }, [\n  // Force update deps\n  mark,\n  // Normal deps\n  record, children, dataIndex, render, renderIndex], function (prev, next) {\n    if (shouldCellUpdate) {\n      var _prev = _slicedToArray(prev, 2),\n        prevRecord = _prev[1];\n      var _next = _slicedToArray(next, 2),\n        nextRecord = _next[1];\n      return shouldCellUpdate(nextRecord, prevRecord);\n    }\n\n    // Legacy mode should always update\n    if (perfRecord.renderWithProps) {\n      return true;\n    }\n    return !isEqual(prev, next, true);\n  });\n  return retData;\n}"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAOC,OAAO,MAAM,mCAAmC;AACvD,OAAOC,OAAO,MAAM,0BAA0B;AAC9C,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAOC,QAAQ,MAAM,sBAAsB;AAC3C,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,WAAW,MAAM,wBAAwB;AAChD,SAASC,aAAa,QAAQ,oBAAoB;AAClD,SAASC,gBAAgB,QAAQ,yBAAyB;AAC1D,SAASC,YAAYA,CAACC,IAAI,EAAE;EAC1B,OAAOA,IAAI,IAAIV,OAAO,CAACU,IAAI,CAAC,KAAK,QAAQ,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,IAAI,CAAC,IAAI,EAAE,aAAaL,KAAK,CAACQ,cAAc,CAACH,IAAI,CAAC;AAChH;AACA,eAAe,SAASI,aAAaA,CAACC,MAAM,EAAEC,SAAS,EAAEC,WAAW,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,gBAAgB,EAAE;EACxG;EACA,IAAIC,UAAU,GAAGhB,KAAK,CAACiB,UAAU,CAAChB,WAAW,CAAC;EAC9C,IAAIiB,IAAI,GAAGf,gBAAgB,CAAC,CAAC;;EAE7B;EACA,IAAIgB,OAAO,GAAGvB,OAAO,CAAC,YAAY;IAChC,IAAIM,aAAa,CAACW,QAAQ,CAAC,EAAE;MAC3B,OAAO,CAACA,QAAQ,CAAC;IACnB;IACA,IAAIO,IAAI,GAAGT,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAKU,SAAS,IAAIV,SAAS,KAAK,EAAE,GAAG,EAAE,GAAGL,KAAK,CAACC,OAAO,CAACI,SAAS,CAAC,GAAGA,SAAS,GAAG,CAACA,SAAS,CAAC;IACtI,IAAIW,KAAK,GAAGxB,QAAQ,CAACY,MAAM,EAAEU,IAAI,CAAC;;IAElC;IACA,IAAIG,eAAe,GAAGD,KAAK;IAC3B,IAAIE,eAAe,GAAGH,SAAS;IAC/B,IAAIP,MAAM,EAAE;MACV,IAAIW,UAAU,GAAGX,MAAM,CAACQ,KAAK,EAAEZ,MAAM,EAAEE,WAAW,CAAC;MACnD,IAAIR,YAAY,CAACqB,UAAU,CAAC,EAAE;QAC5B,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;UACzC7B,OAAO,CAAC,KAAK,EAAE,gGAAgG,CAAC;QAClH;QACAwB,eAAe,GAAGE,UAAU,CAACZ,QAAQ;QACrCW,eAAe,GAAGC,UAAU,CAACI,KAAK;QAClCb,UAAU,CAACc,eAAe,GAAG,IAAI;MACnC,CAAC,MAAM;QACLP,eAAe,GAAGE,UAAU;MAC9B;IACF;IACA,OAAO,CAACF,eAAe,EAAEC,eAAe,CAAC;EAC3C,CAAC,EAAE;EACH;EACAN,IAAI;EACJ;EACAR,MAAM,EAAEG,QAAQ,EAAEF,SAAS,EAAEG,MAAM,EAAEF,WAAW,CAAC,EAAE,UAAUmB,IAAI,EAAEC,IAAI,EAAE;IACvE,IAAIjB,gBAAgB,EAAE;MACpB,IAAIkB,KAAK,GAAGvC,cAAc,CAACqC,IAAI,EAAE,CAAC,CAAC;QACjCG,UAAU,GAAGD,KAAK,CAAC,CAAC,CAAC;MACvB,IAAIE,KAAK,GAAGzC,cAAc,CAACsC,IAAI,EAAE,CAAC,CAAC;QACjCI,UAAU,GAAGD,KAAK,CAAC,CAAC,CAAC;MACvB,OAAOpB,gBAAgB,CAACqB,UAAU,EAAEF,UAAU,CAAC;IACjD;;IAEA;IACA,IAAIlB,UAAU,CAACc,eAAe,EAAE;MAC9B,OAAO,IAAI;IACb;IACA,OAAO,CAACjC,OAAO,CAACkC,IAAI,EAAEC,IAAI,EAAE,IAAI,CAAC;EACnC,CAAC,CAAC;EACF,OAAOb,OAAO;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}