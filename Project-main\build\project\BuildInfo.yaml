---
compiled_package_info:
  package_name: project
  address_alias_instantiation:
    Extensions: "0000000000000000000000000000000000000000000000000000000000000001"
    aptos_framework: "0000000000000000000000000000000000000000000000000000000000000001"
    aptos_fungible_asset: 000000000000000000000000000000000000000000000000000000000000000a
    aptos_std: "0000000000000000000000000000000000000000000000000000000000000001"
    aptos_token: "0000000000000000000000000000000000000000000000000000000000000003"
    core_resources: 000000000000000000000000000000000000000000000000000000000a550c18
    project: 1713bfd2cbd64e222fac265ebe4ea5a5ab48ebee1c28d7ee33885994096c382c
    std: "0000000000000000000000000000000000000000000000000000000000000001"
    vm: "0000000000000000000000000000000000000000000000000000000000000000"
    vm_reserved: "0000000000000000000000000000000000000000000000000000000000000000"
  source_digest: 71DACA1198D12B763D6AB1776AC79AF02B0A1F215AADC09A897138B4B4317FDA
  build_flags:
    dev_mode: false
    test_mode: false
    override_std: ~
    generate_docs: false
    generate_abis: false
    generate_move_model: true
    full_model_generation: true
    install_dir: ~
    force_recompilation: false
    additional_named_addresses: {}
    fetch_deps_only: false
    skip_fetch_latest_git_deps: false
    compiler_config:
      bytecode_version: 7
      known_attributes:
        - bytecode_instruction
        - deprecated
        - event
        - expected_failure
        - "fmt::skip"
        - legacy_entry_fun
        - "lint::allow_unsafe_randomness"
        - "lint::skip"
        - module_lock
        - "mutation::skip"
        - native_interface
        - persistent
        - randomness
        - resource_group
        - resource_group_member
        - test
        - test_only
        - verify_only
        - view
      skip_attribute_checks: false
      compiler_version: V2_0
      language_version: "2.1"
      experiments:
        - optimize=on
dependencies:
  - AptosFramework
  - AptosStdlib
  - MoveStdlib
bytecode_deps: []
