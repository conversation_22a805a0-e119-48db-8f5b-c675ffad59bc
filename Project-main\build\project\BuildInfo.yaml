---
compiled_package_info:
  package_name: project
  address_alias_instantiation:
    Extensions: "0000000000000000000000000000000000000000000000000000000000000001"
    aptos_framework: "0000000000000000000000000000000000000000000000000000000000000001"
    aptos_fungible_asset: 000000000000000000000000000000000000000000000000000000000000000a
    aptos_std: "0000000000000000000000000000000000000000000000000000000000000001"
    aptos_token: "0000000000000000000000000000000000000000000000000000000000000003"
    core_resources: 000000000000000000000000000000000000000000000000000000000a550c18
    project: e5c9ba95ef610003dd37a51333f1e1c4017d7593f1351bd5292d87291a9b730a
    std: "0000000000000000000000000000000000000000000000000000000000000001"
    vm: "0000000000000000000000000000000000000000000000000000000000000000"
    vm_reserved: "0000000000000000000000000000000000000000000000000000000000000000"
  source_digest: 8568D638423CEF667FADE0F18C679AE869280F64BDDF620C6DA6FA14991B81C5
  build_flags:
    dev_mode: false
    test_mode: false
    override_std: ~
    generate_docs: false
    generate_abis: false
    generate_move_model: true
    full_model_generation: true
    install_dir: ~
    force_recompilation: false
    additional_named_addresses: {}
    fetch_deps_only: false
    skip_fetch_latest_git_deps: false
    compiler_config:
      bytecode_version: 7
      known_attributes:
        - bytecode_instruction
        - deprecated
        - event
        - expected_failure
        - "fmt::skip"
        - legacy_entry_fun
        - "lint::allow_unsafe_randomness"
        - "lint::skip"
        - module_lock
        - "mutation::skip"
        - native_interface
        - persistent
        - randomness
        - resource_group
        - resource_group_member
        - test
        - test_only
        - verify_only
        - view
      skip_attribute_checks: false
      compiler_version: V2_0
      language_version: "2.1"
      experiments:
        - optimize=on
dependencies:
  - AptosFramework
  - AptosStdlib
  - MoveStdlib
bytecode_deps: []
